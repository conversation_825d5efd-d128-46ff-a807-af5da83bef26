{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\repairModule\\trackCircuitProducts.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\repairModule\\trackCircuitProducts.vue", "mtime": 1755670347076}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgd3ggZnJvbSAid2VpeGluLWpzLXNkayI7DQppbXBvcnQgeyBtYXBTdGF0ZSwgbWFwTXV0YXRpb25zLCBtYXBHZXR0ZXJzLCBtYXBBY3Rpb25zIH0gZnJvbSAidnVleCI7DQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHt9LA0KICBwcm9wczoge30sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHRyYWNrRGF0YTpbXSwNCiAgICAgIHRpdGxlTGlzdDpbXSwNCiAgICAgIHNlY3Rpb25JbmZvTGlzdDpbXSwNCiAgICAgIHNlbGVjdGVkSW5kZXg6IDAsIC8vIOiusOW9leW9k+WJjemAieS4reeahOmhuQ0KICAgICAgc2VsZWN0ZWRJdGVtOiB7fSwgLy8g6K6w5b2V5b2T5YmN6YCJ5Lit55qE6aG5DQogICAgICBpc3VzZVBvcHVwOmZhbHNlLA0KICAgICAgcG9wdXB0aXRsZTonJywNCiAgICAgIHVuaGlyZWl0ZW06e30sDQogICAgICB2ZXJzaW9uUG9wdXA6ZmFsc2UsDQogICAgLy8gICBjb2x1bW5zOlsnMS4wJywnMS4xJywnMS4yJ10sDQogICAgICBjb2x1bW5zOlsNCiAgICAgICAge3RleHQ6JzEuMCcsdmFsdWU6JzEuMCd9LA0KICAgICAgICB7dGV4dDonMS4xJyx2YWx1ZTonMS4xJ30sDQogICAgICAgIHt0ZXh0OicxLjInLHZhbHVlOicxLjInfSwNCiAgICAgIF0sDQogICAgICBpc1dlQ2hhdCA6ZmFsc2UsDQoNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgICAuLi5tYXBHZXR0ZXJzKFsic2NhbkNvZGVJbmZvIl0pLA0KICB9LA0KICB3YXRjaDoge30sDQogIC8v5pa55rOV6ZuG5ZCIDQogIG1ldGhvZHM6IHsNCiAgICAuLi5tYXBBY3Rpb25zKCJyZXBhaXJfbW9kdWxlcy9yZXBhaXIiLCBbDQogICAgICAiZ2V0Q29uZmlnSW5mb09uZSIsDQogICAgICAgImNhdGVnb3J5TGlzdCIsDQogICAgICAgIm51bWJlclZlcnNpb25MaXN0IiwNCiAgICAgICAiYmluZE9yVW5iaW5kIg0KICAgIF0pLA0KICAgICAuLi5tYXBNdXRhdGlvbnMoInJlcGFpcl9tb2R1bGVzL3JlcGFpciIsIFsic2V0U2NhbkNvZGUiXSksDQogICAgb25DbGlja0xlZnQoKXsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSkNCiAgICB9LA0KICAgIHNlbGVjdEl0ZW0oaW5kZXgsaXRlbSkgew0KICAgICAgICBjb25zb2xlLmxvZyhpdGVtKTsNCiAgICAgICAgLy8gdGhpcy5zZWxlY3RlZEl0ZW09aXRlbQ0KICAgICAgICB0aGlzLnBvcHVwdGl0bGU9aXRlbS50cmFja0NpcmN1aXRDb25maWcuZHJhd2luZ05hbWUNCiAgICAgIC8vIOWmguaenOeCueWHu+eahOaYr+W3sumAieS4reeahOmhue+8jOWImeWPlua2iOmAieaLqe+8jOWQpuWImemAieS4reivpemhuQ0KICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gdGhpcy5zZWxlY3RlZEluZGV4ID09PSBpbmRleCA/IG51bGwgOiBpbmRleDsNCiAgICAgIHRoaXMuZ2V0ZGF0YShpdGVtLnRyYWNrQ2lyY3VpdENvbmZpZy5pZCkNCiAgICB9LA0KICAgIGFzeW5jIGdldGNhdGVnb3J5TGlzdCgpew0KICAgICAgICBjb25zdCByZXM9YXdhaXQgdGhpcy5jYXRlZ29yeUxpc3Qoew0KICAgICAgICAgIHN0YXRpb25JZDp0aGlzLiRyb3V0ZS5xdWVyeS5zdGF0aW9uSWQNCiAgICAgICAgfSkNCiAgICAgICAgaWYocmVzLmNvZGU9PTApew0KICAgICAgICAgICAgDQogICAgICAgICAgdGhpcy50cmFja0RhdGEgPSB0aGlzLiRyb3V0ZS5xdWVyeS50eXBlPT0xPyByZXMuZGF0YVswXTpyZXMuZGF0YVsxXQ0KICAgICAgICAgIHRoaXMucG9wdXB0aXRsZT10aGlzLiRyb3V0ZS5xdWVyeS50eXBlPT0xPw0KICAgICAgICAgICByZXMuZGF0YVswXS5jaGlsZHJlblswXS50cmFja0NpcmN1aXRDb25maWcuZHJhd2luZ05hbWU6cmVzLmRhdGFbMV0uY2hpbGRyZW5bMF0udHJhY2tDaXJjdWl0Q29uZmlnLmRyYXdpbmdOYW1lDQogICAgICAgICAgaWYodGhpcy4kcm91dGUucXVlcnkudHlwZT09MSl7DQogICAgICAgICAgICAgIHRoaXMuZ2V0ZGF0YSh0aGlzLnRyYWNrRGF0YS5jaGlsZHJlblswXS50cmFja0NpcmN1aXRDb25maWcuaWQpDQogICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICB0aGlzLmdldGRhdGEodGhpcy50cmFja0RhdGEuY2hpbGRyZW5bMF0udHJhY2tDaXJjdWl0Q29uZmlnLmlkKQ0KICAgICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGdldGRhdGEoY2F0ZWdvcnlJZCl7DQogICAgICAgIGNvbnN0IHJlcz1hd2FpdCB0aGlzLmdldENvbmZpZ0luZm9PbmUoew0KICAgICAgICAgICAgY29uZmlnSWQ6Y2F0ZWdvcnlJZCwNCiAgICAgICAgICAgIHN0YXRpb25JZDp0aGlzLiRyb3V0ZS5xdWVyeS5zdGF0aW9uSWQsDQogICAgICAgICAgICBjYXRlZ29yeUlkOnRoaXMuJHJvdXRlLnF1ZXJ5LmNhdGVnb3J5SWQsDQogICAgICAgIH0pDQogICAgICAgIGlmKHJlcy5jb2RlPT0wKXsNCiAgICAgICAgICAgIHRoaXMudGl0bGVMaXN0PXJlcy5kYXRhLnNlY3Rpb25JbmZvTGlzdFswXS5jb25maWdJbmZvTGlzdA0KICAgICAgICAgICAgdGhpcy5zZWN0aW9uSW5mb0xpc3Q9cmVzLmRhdGEuc2VjdGlvbkluZm9MaXN0DQogICAgICAgICAgICANCiAgICAgICAgfQ0KICAgIH0sDQogICAgIC8vIOWIpOaWreaYr+WQpuWcqOW+ruS/oeeOr+Wigw0KICAgIGlzTWluaVByb2dyYW0oY2FsbGJhY2spIHsNCiAgICAgIHZhciB1YSA9IHdpbmRvdy5uYXZpZ2F0b3IudXNlckFnZW50LnRvTG93ZXJDYXNlKCk7DQogICAgICBpZiAodWEubWF0Y2goL01pY3JvTWVzc2VuZ2VyL2kpICE9ICJtaWNyb21lc3NlbmdlciIpIHsNCiAgICAgICAgY2FsbGJhY2soZmFsc2UpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgd3gubWluaVByb2dyYW0uZ2V0RW52KChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLm1pbmlwcm9ncmFtKSB7DQogICAgICAgICAgICBjYWxsYmFjayh0cnVlKTsgLy/lsI/nqIvluo/njq/looMNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY2FsbGJhY2soZmFsc2UpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBjaGVja1R5cGVGdW4oaXRlbSx2YWx1ZSl7DQogICAgICAgDQogICAgICAgIGlmKHZhbHVlLmlzVXNlKXsNCiAgICAgICAgICAgICB0aGlzLnVuaGlyZSh2YWx1ZSkNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3NlY3Rpb25JbmZvSXRlbScsSlNPTi5zdHJpbmdpZnkodmFsdWUpKQ0KICAgICAgICAgICAgIHRoaXMuaXNNaW5pUHJvZ3JhbSgocmVzKT0+ew0KICAgICAgICAgICAgICAgIGlmKHJlcyl7DQogICAgICAgICAgICAgICAgICAgICB0aGlzLmlzV2VDaGF0ID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgICBwYXRoOicvcmVwYWlyL3NjYW5Db2RlJywNCiAgICAgICAgICAgIHF1ZXJ5OnsNCiAgICAgICAgICAgICAgICB0eXBlOidjb3VyaWVyQ29kZScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgICAgICAgIHRoaXMuaXNXZUNoYXQgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICAgICAgICAgICAgcGF0aDondHJhY2tDaXJjdWl0L3FyY29kZScsDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgfSkNCiAgICAgICANCiAgICAgICAgICAgICAvLyAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIC8vICAgICBwYXRoOicvcmVwYWlyL3NjYW5Db2RlJywNCiAgICAgICAgLy8gICAgIHF1ZXJ5OnsNCiAgICAgICAgLy8gICAgICAgICB0eXBlOidjb3VyaWVyQ29kZScNCiAgICAgICAgLy8gICAgIH0NCiAgICAgICAgLy8gICAgfSkNCiAgICAgICAgfQ0KICAgICAgDQoNCiAgICB9LA0KICAgIGNsb3NlUG9wdXAoKXsNCiAgICAgICAgdGhpcy5pc3VzZVBvcHVwPWZhbHNlDQogICAgICAgIHRoaXMucG9wdXB0aXRsZT0nJw0KICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnc2VjdGlvbkluZm9JdGVtJykNCiAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdjc3FyY29kZScpDQogICAgfSwNCiAgICBhc3luYyB1bmhpcmUodmFsdWUpew0KICAgICAgICBjb25zb2xlLmxvZyh2YWx1ZSk7DQogICAgICAgIA0KICAgICAgICB0aGlzLmlzdXNlUG9wdXA9dHJ1ZQ0KICAgICAgICAgIGNvbnN0IHJlcz1hd2FpdCB0aGlzLm51bWJlclZlcnNpb25MaXN0KHsNCiAgICAgICAgICAgIHNlY3Rpb25JbmZvSWQ6dmFsdWUuaWQNCiAgICAgICAgfSkNCiAgICAgICAgaWYocmVzLmNvZGU9PTApew0KICAgICAgICAgICAgdGhpcy51bmhpcmVpdGVtPXJlcy5kYXRhDQogICAgICAgIH1lbHNlew0KICAgICAgICAgICAgdGhpcy4kdG9hc3QuZmFpbChyZXMubWVnKQ0KICAgICAgICB9DQogICAgfSwNCiAgICAgYXN5bmMgbmV3dW5oaXJlKHZhbHVlKXsNCiAgICAgICAgY29uc29sZS5sb2codmFsdWUpOw0KICAgICAgICANCiAgICAgICAgdGhpcy5pc3VzZVBvcHVwPXRydWUNCiAgICAgICAgICBjb25zdCByZXM9YXdhaXQgdGhpcy5udW1iZXJWZXJzaW9uTGlzdCh7DQogICAgICAgICAgICBzZWN0aW9uSW5mb0lkOkpTT04ucGFyc2UodmFsdWUpLmlkDQogICAgICAgIH0pDQogICAgICAgIGlmKHJlcy5jb2RlPT0wKXsNCiAgICAgICAgICAgIHRoaXMudW5oaXJlaXRlbT1yZXMuZGF0YQ0KICAgICAgICAgICAgaWYodGhpcy5pc1dlQ2hhdCl7DQogICAgICAgICAgICAgICAgIGNvbnN0IHNjYW5Db2RlID0gdGhpcy5zY2FuQ29kZUluZm8/LmNvdXJpZXJDb2RlIHx8ICIiOw0KICAgICAgICAgICAgICAgICB0aGlzLnVuaGlyZWl0ZW0ubnVtYmVyPXNjYW5Db2RlDQogICAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgICBjb25zdCBjb2RlPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjc3FyY29kZScpKQ0KDQogICAgICAgICAgICAgICAgdGhpcy51bmhpcmVpdGVtLm51bWJlcj1jb2RlLmRlY29kZWRUZXh0DQogICAgICAgICAgICB9DQogICAgICAgICAgIA0KICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgIHRoaXMuJHRvYXN0LmZhaWwocmVzLm1lZykNCiAgICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgdW5iaW5kKCl7DQogICAgICAgIA0KICAgICAgICB0aGlzLiRkaWFsb2cuY29uZmlybSh7DQogICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAgICAgICBtZXNzYWdlOiBg5piv5ZCm6KaBJHt0aGlzLnVuaGlyZWl0ZW0uaXNVc2U/J+ino+e7kSc6J+e7keWumid9YCwNCiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiBg56Gu6K6kJHt0aGlzLnVuaGlyZWl0ZW0uaXNVc2U/J+ino+e7kSc6J+e7keWumid9YCwNCiAgICAgICAgfSkudGhlbihhc3luYygpID0+IHsNCiAgICAgICAgICAgIGxldCBvYmo9ew0KICAgICAgICAgICAgICAgIGlkOnRoaXMudW5oaXJlaXRlbS5pZCwNCiAgICAgICAgICAgICAgICBpc1VzZTp0aGlzLnVuaGlyZWl0ZW0uaXNVc2U/ZmFsc2U6dHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYoIXRoaXMudW5oaXJlaXRlbS5pc1VzZSl7DQogICAgICAgICAgICAgICAgb2JqLm51bWJlcj10aGlzLnVuaGlyZWl0ZW0ubnVtYmVyDQogICAgICAgICAgICAgICAgb2JqLnZlcnNpb24gPSB0aGlzLnVuaGlyZWl0ZW0udmVyc2lvbg0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc3QgcmVzPWF3YWl0IHRoaXMuYmluZE9yVW5iaW5kKG9iaikNCiAgICAgICAgICAgIGlmKHJlcy5jb2RlPT0wKXsNCiAgICAgICAgICAgICAgICB0aGlzLnNldFNjYW5Db2RlKHt9KQ0KICAgICAgICAgICAgICAgIHRoaXMuJHRvYXN0LnN1Y2Nlc3MoYCR7dGhpcy51bmhpcmVpdGVtLmlzVXNlPyfop6Pnu5EnOifnu5HlrponfeaIkOWKn++8gWApDQogICAgICAgICAgICAgICAgdGhpcy5jbG9zZVBvcHVwKCkNCiAgICAgICAgICAgICAgICAgdGhpcy5nZXRjYXRlZ29yeUxpc3QoKQ0KICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgdGhpcy4kdG9hc3QuZmFpbChyZXMubXNnfHwn5pON5L2c5aSx6LSlJykNCg0KICAgICAgICAgICAgfQ0KICAgICAgIH0pDQogICAgfSwNCiAgICBvbkNvbmZpcm0odmFsdWUpew0KICAgICAgICB0aGlzLnVuaGlyZWl0ZW0udmVyc2lvbj12YWx1ZS50ZXh0DQogICAgICAgIHRoaXMudmVyc2lvblBvcHVwPWZhbHNlDQogICAgfSwNCiAgICBvbkNhbmNlbCgpew0KICAgICAgICB0aGlzLnZlcnNpb25Qb3B1cD1mYWxzZQ0KICAgIH0sDQogICAgY2xvc2V2ZXJzaW9uUG9wdXAoKXsNCiAgICAgICAgdGhpcy52ZXJzaW9uUG9wdXA9ZmFsc2UNCiAgICB9DQoNCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmlzTWluaVByb2dyYW0oKHJlcykgPT4gew0KICAgICAgaWYgKHJlcykgew0KICAgICAgICBjb25zb2xlLmxvZygiaXNNaW5pUHJvZ3JhbSIpOw0KICAgICAgICB0aGlzLmlzV2VDaGF0ID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXNXZUNoYXQgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9KTsNCiAgfSwNCiAgbW91bnRlZCgpIHsgDQogICAgY29uc29sZS5sb2cobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2NzcXJjb2RlJykpOw0KDQogICAgDQogICAgaWYoIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdzZWN0aW9uSW5mb0l0ZW0nKSl7DQogICAgICAgIGNvbnN0IGl0ZW09bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3NlY3Rpb25JbmZvSXRlbScpDQogICAgICAgIGlmKHRoaXMuc2NhbkNvZGVJbmZvLmNvdXJpZXJDb2RlfHxsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3NxcmNvZGUnKSl7DQogICAgICAgICAgICAgdGhpcy5uZXd1bmhpcmUoaXRlbSkNCiAgICAgICAgfQ0KICAgICAgIA0KICAgIH0NCiAgICB0aGlzLmdldGNhdGVnb3J5TGlzdCgpDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["trackCircuitProducts.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "trackCircuitProducts.vue", "sourceRoot": "src/views/repairModule", "sourcesContent": ["<template>\r\n  <div class='box'>\r\n    <!-- <van-nav-bar\r\n      :title='$route.query.categoryName'\r\n      left-arrow\r\n      @click-left=\"onClickLeft\"\r\n    /> -->\r\n\r\n    <div class=\"header\">\r\n      <div \r\n        class=\"header-title\" \r\n        v-for=\"(item,index) in trackData.children\" \r\n        :key=\"index\"\r\n        :class=\"{ active: selectedIndex === index }\"\r\n        @click=\"selectItem(index,item)\"\r\n      >\r\n        {{ item.trackCircuitConfig.drawingName }}\r\n      </div>\r\n    </div>\r\n    <!--移动柜/电码化发送柜  -->\r\n    <div class=\"content\" v-if=\"$route.query.type==1\">\r\n        <div class=\"content-box\">\r\n            <div class=\"content-left\">\r\n                <div></div>\r\n                <div class=\"content-title\" v-for=\"item in titleList\">\r\n                {{ item.configName}}:\r\n                </div>\r\n                <div></div>\r\n                <div class=\"content-title\" v-for=\"item in titleList\">\r\n                {{ item.configName}}:\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"content-list\">\r\n                <div class=\"item\" v-for=\"item in sectionInfoList\">\r\n                    <div class=\"item-title\">\r\n                        {{ item.sectionName}}\r\n                    </div>\r\n                    <div class=\"item-isUse\" v-for=\"value in item.configInfoList\" @click=\"checkTypeFun(item,value)\">\r\n                        <div class=\"success-icon\"  v-if=\"value.isUse\">\r\n                            <div ><van-icon name=\"success\" /></div>\r\n                            已录</div>\r\n                        <div class=\"error-icon\" v-else><div ></div>未录</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!-- 模拟网络接口柜 -->\r\n    <div class=\"content\" v-if=\"$route.query.type==2\">\r\n        <div class=\"content-monitor\" v-for=\"item in sectionInfoList\" :key=\"item.sectionId\">\r\n            <div class=\"content-monitor-title\">\r\n                {{ item.sectionName }}\r\n            </div>\r\n            <div class=\"content-monitor-item\" >\r\n                <div class=\"monior-item\" v-for=\"item1 in item.configInfoList\r\n\" :key=\"item1.id\">\r\n                    <div class=\"title\">{{ item1.configName }}</div>\r\n                    <div :class=\"item1.isUse?'isusepass':'isusechange'\" @click=\"checkTypeFun(item,item1)\">\r\n                        <van-icon name=\"success\" v-if=\"item1.isUse\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!-- 已录弹出层 -->\r\n     <van-popup v-model=\"isusePopup\" @close=\"closePopup\" position=\"bottom\" :style=\"{ height: '50%' }\" >\r\n         <div class=\"popup-container\">\r\n            <div class=\"popup-header\">\r\n                <h3 class=\"popup-title\">{{ popuptitle }}</h3>\r\n            </div>\r\n            <div class=\"popup-sectionName\">\r\n                {{ unhireitem.configName }}\r\n            </div>\r\n            <div class=\"popup-sectionName\" v-if=\"$route.query.type==2\">\r\n                {{ unhireitem.sectionName }}\r\n            </div>\r\n            <div class=\"popup-form\">\r\n                <div class=\"popup-form-item\" style=\"display: flex;align-items: center;font-size: 14px;\">\r\n                    <!-- <van-field\r\n                        v-model=\"unhireitem.number\"\r\n                        center\r\n                        clearable\r\n                        readonly\r\n                        label-width=\"7rem\"\r\n                        :type=\"unhireitem.number&&unhireitem.number.length>=30?'textarea':''\"\r\n                        >\r\n                        <template #label>\r\n                           <div class=\"label-text\">\r\n                            序列号：\r\n                           </div> \r\n                        </template>\r\n                        <template #button>\r\n                            <van-button size=\"small\" type=\"info\" @click=\"unbind\">{{unhireitem.isUse?'解绑':'绑定'}}</van-button>\r\n                        </template>\r\n                        </van-field> -->\r\n                        <!-- <div class=\"popup-form-item-title\">\r\n                            <div class=\"title\">序列号：</div>\r\n                            <div class=\"number\">{{ unhireitem.number }}</div>\r\n                            <div class=\"btn\">\r\n                                 <van-button size=\"small\" type=\"info\" @click=\"unbind\">{{unhireitem.isUse?'解绑':'绑定'}}</van-button>\r\n                            </div>\r\n                        </div> -->\r\n                           <div class=\"title\" style=\"width: 8rem;font-size: 14px;color: #015a9d;\">序列号：</div>\r\n                            <div class=\"serial-number-display\" style=\"flex: 1; padding: 0 10px;  white-space: wrap;\">\r\n                                {{ unhireitem.number }}\r\n                            </div>\r\n                            <van-button style=\"width: 65px;\" size=\"small\" type=\"info\" @click=\"unbind\">\r\n                                {{unhireitem.isUse?'解绑':'绑定'}}\r\n                            </van-button>\r\n                </div>\r\n                 <div class=\"popup-form-item\">\r\n                     <van-field\r\n                        v-model=\"unhireitem.version\"\r\n                        center\r\n                        clearable\r\n                        label-width=\"7rem\"\r\n                        type=\"number\"\r\n                        :readonly=\"unhireitem.isUse\"\r\n                       \r\n                        >\r\n                          <!-- @click=\"versionPopup = true\" -->\r\n                        <template #label>\r\n                           <div class=\"label-text\">\r\n                            版本号：\r\n                           </div> \r\n                        </template>\r\n                        </van-field>\r\n                 </div>\r\n            </div>\r\n        </div>\r\n     </van-popup>\r\n     <!-- 版本弹出层 -->\r\n    <van-popup v-model=\"versionPopup\"  round\r\n      :style=\"{ height: '60%' }\" @close=\"closeversionPopup\" :default-index=\"0\" position=\"bottom\" >\r\n            <van-picker\r\n            title=\"版本选择\"  \r\n            show-toolbar\r\n            :columns=\"columns\"\r\n            @confirm=\"onConfirm\"\r\n            @cancel=\"onCancel\"\r\n            />\r\n    </van-popup>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport wx from \"weixin-js-sdk\";\r\nimport { mapState, mapMutations, mapGetters, mapActions } from \"vuex\";\r\nexport default {\r\n  components: {},\r\n  props: {},\r\n  data() {\r\n    return {\r\n      trackData:[],\r\n      titleList:[],\r\n      sectionInfoList:[],\r\n      selectedIndex: 0, // 记录当前选中的项\r\n      selectedItem: {}, // 记录当前选中的项\r\n      isusePopup:false,\r\n      popuptitle:'',\r\n      unhireitem:{},\r\n      versionPopup:false,\r\n    //   columns:['1.0','1.1','1.2'],\r\n      columns:[\r\n        {text:'1.0',value:'1.0'},\r\n        {text:'1.1',value:'1.1'},\r\n        {text:'1.2',value:'1.2'},\r\n      ],\r\n      isWeChat :false,\r\n\r\n    };\r\n  },\r\n  computed: {\r\n     ...mapGetters([\"scanCodeInfo\"]),\r\n  },\r\n  watch: {},\r\n  //方法集合\r\n  methods: {\r\n    ...mapActions(\"repair_modules/repair\", [\r\n      \"getConfigInfoOne\",\r\n       \"categoryList\",\r\n       \"numberVersionList\",\r\n       \"bindOrUnbind\"\r\n    ]),\r\n     ...mapMutations(\"repair_modules/repair\", [\"setScanCode\"]),\r\n    onClickLeft(){\r\n      this.$router.go(-1)\r\n    },\r\n    selectItem(index,item) {\r\n        console.log(item);\r\n        // this.selectedItem=item\r\n        this.popuptitle=item.trackCircuitConfig.drawingName\r\n      // 如果点击的是已选中的项，则取消选择，否则选中该项\r\n      this.selectedIndex = this.selectedIndex === index ? null : index;\r\n      this.getdata(item.trackCircuitConfig.id)\r\n    },\r\n    async getcategoryList(){\r\n        const res=await this.categoryList({\r\n          stationId:this.$route.query.stationId\r\n        })\r\n        if(res.code==0){\r\n            \r\n          this.trackData = this.$route.query.type==1? res.data[0]:res.data[1]\r\n          this.popuptitle=this.$route.query.type==1?\r\n           res.data[0].children[0].trackCircuitConfig.drawingName:res.data[1].children[0].trackCircuitConfig.drawingName\r\n          if(this.$route.query.type==1){\r\n              this.getdata(this.trackData.children[0].trackCircuitConfig.id)\r\n          }else{\r\n            this.getdata(this.trackData.children[0].trackCircuitConfig.id)\r\n          }\r\n        \r\n        }\r\n    },\r\n    async getdata(categoryId){\r\n        const res=await this.getConfigInfoOne({\r\n            configId:categoryId,\r\n            stationId:this.$route.query.stationId,\r\n            categoryId:this.$route.query.categoryId,\r\n        })\r\n        if(res.code==0){\r\n            this.titleList=res.data.sectionInfoList[0].configInfoList\r\n            this.sectionInfoList=res.data.sectionInfoList\r\n            \r\n        }\r\n    },\r\n     // 判断是否在微信环境\r\n    isMiniProgram(callback) {\r\n      var ua = window.navigator.userAgent.toLowerCase();\r\n      if (ua.match(/MicroMessenger/i) != \"micromessenger\") {\r\n        callback(false);\r\n      } else {\r\n        wx.miniProgram.getEnv((res) => {\r\n          if (res.miniprogram) {\r\n            callback(true); //小程序环境\r\n          } else {\r\n            callback(false);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    async checkTypeFun(item,value){\r\n       \r\n        if(value.isUse){\r\n             this.unhire(value)\r\n        }else{\r\n             localStorage.setItem('sectionInfoItem',JSON.stringify(value))\r\n             this.isMiniProgram((res)=>{\r\n                if(res){\r\n                     this.isWeChat = true;\r\n                            this.$router.push({\r\n            path:'/repair/scanCode',\r\n            query:{\r\n                type:'courierCode'\r\n            }\r\n           })\r\n                }else{\r\n                     this.isWeChat = false;\r\n                     this.$router.push({\r\n                    path:'trackCircuit/qrcode',\r\n                })\r\n                }\r\n             })\r\n       \r\n             //    this.$router.push({\r\n        //     path:'/repair/scanCode',\r\n        //     query:{\r\n        //         type:'courierCode'\r\n        //     }\r\n        //    })\r\n        }\r\n      \r\n\r\n    },\r\n    closePopup(){\r\n        this.isusePopup=false\r\n        this.popuptitle=''\r\n        localStorage.removeItem('sectionInfoItem')\r\n         localStorage.removeItem('csqrcode')\r\n    },\r\n    async unhire(value){\r\n        console.log(value);\r\n        \r\n        this.isusePopup=true\r\n          const res=await this.numberVersionList({\r\n            sectionInfoId:value.id\r\n        })\r\n        if(res.code==0){\r\n            this.unhireitem=res.data\r\n        }else{\r\n            this.$toast.fail(res.meg)\r\n        }\r\n    },\r\n     async newunhire(value){\r\n        console.log(value);\r\n        \r\n        this.isusePopup=true\r\n          const res=await this.numberVersionList({\r\n            sectionInfoId:JSON.parse(value).id\r\n        })\r\n        if(res.code==0){\r\n            this.unhireitem=res.data\r\n            if(this.isWeChat){\r\n                 const scanCode = this.scanCodeInfo?.courierCode || \"\";\r\n                 this.unhireitem.number=scanCode\r\n            }else{\r\n                const code= JSON.parse(localStorage.getItem('csqrcode'))\r\n\r\n                this.unhireitem.number=code.decodedText\r\n            }\r\n           \r\n        }else{\r\n            this.$toast.fail(res.meg)\r\n        }\r\n    },\r\n    async unbind(){\r\n        \r\n        this.$dialog.confirm({\r\n            title: '提示',\r\n            message: `是否要${this.unhireitem.isUse?'解绑':'绑定'}`,\r\n            confirmButtonText: `确认${this.unhireitem.isUse?'解绑':'绑定'}`,\r\n        }).then(async() => {\r\n            let obj={\r\n                id:this.unhireitem.id,\r\n                isUse:this.unhireitem.isUse?false:true\r\n            }\r\n            if(!this.unhireitem.isUse){\r\n                obj.number=this.unhireitem.number\r\n                obj.version = this.unhireitem.version\r\n            }\r\n            const res=await this.bindOrUnbind(obj)\r\n            if(res.code==0){\r\n                this.setScanCode({})\r\n                this.$toast.success(`${this.unhireitem.isUse?'解绑':'绑定'}成功！`)\r\n                this.closePopup()\r\n                 this.getcategoryList()\r\n            }else{\r\n                this.$toast.fail(res.msg||'操作失败')\r\n\r\n            }\r\n       })\r\n    },\r\n    onConfirm(value){\r\n        this.unhireitem.version=value.text\r\n        this.versionPopup=false\r\n    },\r\n    onCancel(){\r\n        this.versionPopup=false\r\n    },\r\n    closeversionPopup(){\r\n        this.versionPopup=false\r\n    }\r\n\r\n  },\r\n  async created() {\r\n    this.isMiniProgram((res) => {\r\n      if (res) {\r\n        console.log(\"isMiniProgram\");\r\n        this.isWeChat = true;\r\n      } else {\r\n        this.isWeChat = false;\r\n      }\r\n    });\r\n  },\r\n  mounted() { \r\n    console.log(localStorage.getItem('csqrcode'));\r\n\r\n    \r\n    if( localStorage.getItem('sectionInfoItem')){\r\n        const item=localStorage.getItem('sectionInfoItem')\r\n        if(this.scanCodeInfo.courierCode||localStorage.getItem('csqrcode')){\r\n             this.newunhire(item)\r\n        }\r\n       \r\n    }\r\n    this.getcategoryList()\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.box{\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #f0f8ff;\r\n  // padding: 8px 10px 0px;\r\n  box-sizing: border-box;\r\n  overflow: auto;\r\n//   display: flex;\r\n//   flex-direction: column;\r\n}\r\n.header{\r\n    width: 100%;\r\n    padding: 10px 20px;\r\n    background-color: #fff;\r\n    display: flex;\r\n    \r\n}\r\n.header-title {\r\n  padding: 5px 10px;\r\n  margin: 5px;\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  border: 1px solid #000;\r\n\r\n}\r\n\r\n.header-title.active {\r\n  background-color: #1989fa;\r\n  color: #fff;\r\n  border: none;\r\n}\r\n.content{\r\n    width: 100%;\r\n    padding: 10px;\r\n    margin-top:10px ;\r\n    background: #fff;\r\n}\r\n.content-box{\r\n    width: 100%;\r\n    display: flex;\r\n}\r\n.content-left{\r\n    width: 15%;\r\n    div{\r\n        width: 100%;\r\n        height: 60px;\r\n        line-height: 60px;\r\n        font-size: 16px;\r\n        text-align: center;\r\n        color: #015a9d;\r\n        font-family: 黑体;\r\n        font-weight: 700;\r\n    }\r\n}\r\n.content-list{\r\n    flex: 1;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-evenly;\r\n    .item{\r\n        width: 19%;\r\n        .item-title{\r\n            color: #575757;\r\n            height: 60px;\r\n            line-height: 60px;\r\n            font-size: 16px;\r\n            font-family: 黑体;\r\n            font-weight: 700;\r\n        }\r\n        .item-isUse{\r\n            height: 60px;\r\n            line-height: 60px;\r\n            font-size: 16px;\r\n            .success-icon{\r\n                display: flex;\r\n                align-items: center;\r\n                color: #07c160;\r\n                cursor: pointer;\r\n                div{\r\n                    width: 20px;\r\n                    height: 20px;\r\n                    border-radius: 50%;\r\n                    display: flex;\r\n                    justify-content: center;\r\n                    align-items: center;\r\n                    border: 1px dashed #07c160\r\n                }\r\n            }\r\n            .error-icon{\r\n                display: flex;\r\n                align-items: center;\r\n                color: #7c7575;\r\n                  cursor: pointer;\r\n                div{\r\n                    width: 15px;\r\n                    height: 15px;\r\n                    border-radius: 50%;\r\n                    \r\n                    border: 1px dashed #a3a3a3\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n// Popup 弹出层样式\r\n.popup-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  background: white;\r\n}\r\n\r\n.popup-header {\r\n  padding: 20px 20px 0 20px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .popup-title {\r\n    font-size: 15px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin: 0;\r\n    padding-bottom: 16px;\r\n    text-align: center;\r\n  }\r\n  \r\n}\r\n.popup-sectionName{\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 20px 15px;\r\n    font-weight: 600;\r\n     color: #015a9d;;\r\n  }\r\n  .popup-form{\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    .popup-form-item{\r\n        width: 100%;\r\n        padding: 10px;\r\n\r\n        .popup-form-item-title{\r\n            width: 100%;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            font-size: 14px;\r\n            .title{\r\n                color: #015a9d;\r\n            }\r\n            .number{\r\n                display: flex;\r\n                flex-wrap: wrap;\r\n                width: 60%;\r\n            }\r\n        }\r\n    }\r\n  }\r\n.label-text{\r\n    font-size: 14px;\r\n     color: #015a9d;;\r\n}\r\n.content-monitor{ \r\n    width: 100%;\r\n    .content-monitor-title{\r\n        width: 100%;\r\n        color: #333;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        padding: 10px;\r\n    }\r\n    .content-monitor-item{\r\n        width: 100%;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        // justify-content: space-evenly;\r\n        .monior-item{\r\n            width: 22%;\r\n            height: 60px;\r\n            display: flex;\r\n            justify-content: space-around;\r\n            align-items: center;\r\n            margin: 10px 0;\r\n            flex-direction: column;\r\n            margin: 0 5px;\r\n            .title{\r\n                width: 100%;\r\n                font-size: 14px;\r\n                text-align: center;\r\n            }\r\n            .isusechange{\r\n                font-size: 20px;\r\n                width: 20px;\r\n                height: 20px;\r\n                border-radius: 50%;\r\n                \r\n                border: 1px dashed #a3a3a3\r\n            }\r\n            .isusepass{\r\n                font-size: 20px;\r\n                width: 20px;\r\n                height: 20px;\r\n                display: flex;\r\n                justify-content: center;\r\n                align-items: center;\r\n                border-radius: 50%;\r\n                color: #fff;\r\n                background: #07c160;\r\n                border: 1px dashed #07c160\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"]}]}