{"remainingRequest": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js!E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js!E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\return\\returnWeb.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\return\\returnWeb.vue", "mtime": 1755573917690}, {"path": "E:\\smallProgram\\babel.config.js", "mtime": 1753929329699}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751509197733}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}