<template>
    <div class="main">
        <!-- 居中提示文本 -->
        <div class="tip-container">
            <p class="tip-text">请在Web端操作</p>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapActions, mapMutations } from "vuex"
import { tokenGuard, toolGuard } from "@/utils"
export default {
    name: "returnWeb",
    data () {
        return {
        }
    },
    watch: {},
    created () {
        document.title = '钉钉提示'
        //判断浏览器的类型
        let ua = navigator.userAgent.toLowerCase()
        console.log('判断浏览器的类型', String(ua).includes("dingtalk"),)
        if (String(ua).includes("dingtalk")) {
            if (!tokenGuard.getToken()) {
                this.DingCode()
            }
        }
    },
    methods: {
        ...mapMutations("userLogin_modules/userLogin", ["setUserData"]),
        ...mapActions("superviseMatter_modules/superviseMatter", [
            "dingUserInfoDetailLogin", //微应用免登|参数就一个免登码
        ]),
        DingCode () {
            //钉钉
            this.ddGetAuthCode({
                corpId: 'dingc6ea79f3e805963a4ac5d6980864d335',//通号
                success: (res) => {
                    this.init(res.code)
                },
                fail: (error) => {
                    console.log("dd失败")
                },
                complete: (res) => {
                    console.log("dd完成")
                },
            })
        },
        async init (code) {
            const res = await this.dingUserInfoDetailLogin({
                code: code,
            })
            if (res && res.code == 0) {
                // 清除老数据
                tokenGuard.removeMsgOnceTime()
                tokenGuard.setToken(res.msg)
                localStorage.setItem("userInfo", JSON.stringify(res.data))
                await this.setUserData(res.data)
            } else {
                this.$message.error(res.msg || "登陆失败")
            }
        },
    },
}
</script>

<style scoped lang="less">
.main {
    /* 让容器占满整个屏幕 */
    width: 100vw;
    height: 100vh;
    /* 使用Flex布局居中内容 */
    display: flex;
    justify-content: center;
    align-items: center;
    /* 可选：添加背景色区分区域 */
    background-color: #f5f5f5;
}

.tip-container {
    /* 文本容器样式 */
    text-align: center;
    padding: 20px;
}

.tip-text {
    /* 文本样式 */
    font-size: 18px;
    color: #333;
    font-weight: 500;
    /* 可选：添加图标增强提示 */
    &::before {
        content: "ⓘ";
        display: inline-block;
        margin-right: 8px;
        color: #1890ff;
        font-size: 20px;
    }
}
</style>