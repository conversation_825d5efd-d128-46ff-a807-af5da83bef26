{"remainingRequest": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js!E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js!E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\repairModule\\trackCircuitProducts.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\repairModule\\trackCircuitProducts.vue", "mtime": 1755670347076}, {"path": "E:\\smallProgram\\babel.config.js", "mtime": 1753929329699}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751509197733}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzWyJkZWZhdWx0Il0gPSB2b2lkIDA7CnZhciBfd2VpeGluSnNTZGsgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIndlaXhpbi1qcy1zZGsiKSk7CnZhciBfdnVleCA9IHJlcXVpcmUoInZ1ZXgiKTsKZnVuY3Rpb24gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChvYmopIHsgcmV0dXJuIG9iaiAmJiBvYmouX19lc01vZHVsZSA/IG9iaiA6IHsgImRlZmF1bHQiOiBvYmogfTsgfQpmdW5jdGlvbiBfdHlwZW9mKG8pIHsgIkBiYWJlbC9oZWxwZXJzIC0gdHlwZW9mIjsgcmV0dXJuIF90eXBlb2YgPSAiZnVuY3Rpb24iID09IHR5cGVvZiBTeW1ib2wgJiYgInN5bWJvbCIgPT0gdHlwZW9mIFN5bWJvbC5pdGVyYXRvciA/IGZ1bmN0aW9uIChvKSB7IHJldHVybiB0eXBlb2YgbzsgfSA6IGZ1bmN0aW9uIChvKSB7IHJldHVybiBvICYmICJmdW5jdGlvbiIgPT0gdHlwZW9mIFN5bWJvbCAmJiBvLmNvbnN0cnVjdG9yID09PSBTeW1ib2wgJiYgbyAhPT0gU3ltYm9sLnByb3RvdHlwZSA/ICJzeW1ib2wiIDogdHlwZW9mIG87IH0sIF90eXBlb2Yobyk7IH0KZnVuY3Rpb24gX3JlZ2VuZXJhdG9yUnVudGltZSgpIHsgInVzZSBzdHJpY3QiOyAvKiEgcmVnZW5lcmF0b3ItcnVudGltZSAtLSBDb3B5cmlnaHQgKGMpIDIwMTQtcHJlc2VudCwgRmFjZWJvb2ssIEluYy4gLS0gbGljZW5zZSAoTUlUKTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlZ2VuZXJhdG9yL2Jsb2IvbWFpbi9MSUNFTlNFICovIF9yZWdlbmVyYXRvclJ1bnRpbWUgPSBmdW5jdGlvbiBfcmVnZW5lcmF0b3JSdW50aW1lKCkgeyByZXR1cm4gZTsgfTsgdmFyIHQsIGUgPSB7fSwgciA9IE9iamVjdC5wcm90b3R5cGUsIG4gPSByLmhhc093blByb3BlcnR5LCBvID0gT2JqZWN0LmRlZmluZVByb3BlcnR5IHx8IGZ1bmN0aW9uICh0LCBlLCByKSB7IHRbZV0gPSByLnZhbHVlOyB9LCBpID0gImZ1bmN0aW9uIiA9PSB0eXBlb2YgU3ltYm9sID8gU3ltYm9sIDoge30sIGEgPSBpLml0ZXJhdG9yIHx8ICJAQGl0ZXJhdG9yIiwgYyA9IGkuYXN5bmNJdGVyYXRvciB8fCAiQEBhc3luY0l0ZXJhdG9yIiwgdSA9IGkudG9TdHJpbmdUYWcgfHwgIkBAdG9TdHJpbmdUYWciOyBmdW5jdGlvbiBkZWZpbmUodCwgZSwgcikgeyByZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsIGUsIHsgdmFsdWU6IHIsIGVudW1lcmFibGU6ICEwLCBjb25maWd1cmFibGU6ICEwLCB3cml0YWJsZTogITAgfSksIHRbZV07IH0gdHJ5IHsgZGVmaW5lKHt9LCAiIik7IH0gY2F0Y2ggKHQpIHsgZGVmaW5lID0gZnVuY3Rpb24gZGVmaW5lKHQsIGUsIHIpIHsgcmV0dXJuIHRbZV0gPSByOyB9OyB9IGZ1bmN0aW9uIHdyYXAodCwgZSwgciwgbikgeyB2YXIgaSA9IGUgJiYgZS5wcm90b3R5cGUgaW5zdGFuY2VvZiBHZW5lcmF0b3IgPyBlIDogR2VuZXJhdG9yLCBhID0gT2JqZWN0LmNyZWF0ZShpLnByb3RvdHlwZSksIGMgPSBuZXcgQ29udGV4dChuIHx8IFtdKTsgcmV0dXJuIG8oYSwgIl9pbnZva2UiLCB7IHZhbHVlOiBtYWtlSW52b2tlTWV0aG9kKHQsIHIsIGMpIH0pLCBhOyB9IGZ1bmN0aW9uIHRyeUNhdGNoKHQsIGUsIHIpIHsgdHJ5IHsgcmV0dXJuIHsgdHlwZTogIm5vcm1hbCIsIGFyZzogdC5jYWxsKGUsIHIpIH07IH0gY2F0Y2ggKHQpIHsgcmV0dXJuIHsgdHlwZTogInRocm93IiwgYXJnOiB0IH07IH0gfSBlLndyYXAgPSB3cmFwOyB2YXIgaCA9ICJzdXNwZW5kZWRTdGFydCIsIGwgPSAic3VzcGVuZGVkWWllbGQiLCBmID0gImV4ZWN1dGluZyIsIHMgPSAiY29tcGxldGVkIiwgeSA9IHt9OyBmdW5jdGlvbiBHZW5lcmF0b3IoKSB7fSBmdW5jdGlvbiBHZW5lcmF0b3JGdW5jdGlvbigpIHt9IGZ1bmN0aW9uIEdlbmVyYXRvckZ1bmN0aW9uUHJvdG90eXBlKCkge30gdmFyIHAgPSB7fTsgZGVmaW5lKHAsIGEsIGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRoaXM7IH0pOyB2YXIgZCA9IE9iamVjdC5nZXRQcm90b3R5cGVPZiwgdiA9IGQgJiYgZChkKHZhbHVlcyhbXSkpKTsgdiAmJiB2ICE9PSByICYmIG4uY2FsbCh2LCBhKSAmJiAocCA9IHYpOyB2YXIgZyA9IEdlbmVyYXRvckZ1bmN0aW9uUHJvdG90eXBlLnByb3RvdHlwZSA9IEdlbmVyYXRvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKHApOyBmdW5jdGlvbiBkZWZpbmVJdGVyYXRvck1ldGhvZHModCkgeyBbIm5leHQiLCAidGhyb3ciLCAicmV0dXJuIl0uZm9yRWFjaChmdW5jdGlvbiAoZSkgeyBkZWZpbmUodCwgZSwgZnVuY3Rpb24gKHQpIHsgcmV0dXJuIHRoaXMuX2ludm9rZShlLCB0KTsgfSk7IH0pOyB9IGZ1bmN0aW9uIEFzeW5jSXRlcmF0b3IodCwgZSkgeyBmdW5jdGlvbiBpbnZva2UociwgbywgaSwgYSkgeyB2YXIgYyA9IHRyeUNhdGNoKHRbcl0sIHQsIG8pOyBpZiAoInRocm93IiAhPT0gYy50eXBlKSB7IHZhciB1ID0gYy5hcmcsIGggPSB1LnZhbHVlOyByZXR1cm4gaCAmJiAib2JqZWN0IiA9PSBfdHlwZW9mKGgpICYmIG4uY2FsbChoLCAiX19hd2FpdCIpID8gZS5yZXNvbHZlKGguX19hd2FpdCkudGhlbihmdW5jdGlvbiAodCkgeyBpbnZva2UoIm5leHQiLCB0LCBpLCBhKTsgfSwgZnVuY3Rpb24gKHQpIHsgaW52b2tlKCJ0aHJvdyIsIHQsIGksIGEpOyB9KSA6IGUucmVzb2x2ZShoKS50aGVuKGZ1bmN0aW9uICh0KSB7IHUudmFsdWUgPSB0LCBpKHUpOyB9LCBmdW5jdGlvbiAodCkgeyByZXR1cm4gaW52b2tlKCJ0aHJvdyIsIHQsIGksIGEpOyB9KTsgfSBhKGMuYXJnKTsgfSB2YXIgcjsgbyh0aGlzLCAiX2ludm9rZSIsIHsgdmFsdWU6IGZ1bmN0aW9uIHZhbHVlKHQsIG4pIHsgZnVuY3Rpb24gY2FsbEludm9rZVdpdGhNZXRob2RBbmRBcmcoKSB7IHJldHVybiBuZXcgZShmdW5jdGlvbiAoZSwgcikgeyBpbnZva2UodCwgbiwgZSwgcik7IH0pOyB9IHJldHVybiByID0gciA/IHIudGhlbihjYWxsSW52b2tlV2l0aE1ldGhvZEFuZEFyZywgY2FsbEludm9rZVdpdGhNZXRob2RBbmRBcmcpIDogY2FsbEludm9rZVdpdGhNZXRob2RBbmRBcmcoKTsgfSB9KTsgfSBmdW5jdGlvbiBtYWtlSW52b2tlTWV0aG9kKGUsIHIsIG4pIHsgdmFyIG8gPSBoOyByZXR1cm4gZnVuY3Rpb24gKGksIGEpIHsgaWYgKG8gPT09IGYpIHRocm93IG5ldyBFcnJvcigiR2VuZXJhdG9yIGlzIGFscmVhZHkgcnVubmluZyIpOyBpZiAobyA9PT0gcykgeyBpZiAoInRocm93IiA9PT0gaSkgdGhyb3cgYTsgcmV0dXJuIHsgdmFsdWU6IHQsIGRvbmU6ICEwIH07IH0gZm9yIChuLm1ldGhvZCA9IGksIG4uYXJnID0gYTs7KSB7IHZhciBjID0gbi5kZWxlZ2F0ZTsgaWYgKGMpIHsgdmFyIHUgPSBtYXliZUludm9rZURlbGVnYXRlKGMsIG4pOyBpZiAodSkgeyBpZiAodSA9PT0geSkgY29udGludWU7IHJldHVybiB1OyB9IH0gaWYgKCJuZXh0IiA9PT0gbi5tZXRob2QpIG4uc2VudCA9IG4uX3NlbnQgPSBuLmFyZztlbHNlIGlmICgidGhyb3ciID09PSBuLm1ldGhvZCkgeyBpZiAobyA9PT0gaCkgdGhyb3cgbyA9IHMsIG4uYXJnOyBuLmRpc3BhdGNoRXhjZXB0aW9uKG4uYXJnKTsgfSBlbHNlICJyZXR1cm4iID09PSBuLm1ldGhvZCAmJiBuLmFicnVwdCgicmV0dXJuIiwgbi5hcmcpOyBvID0gZjsgdmFyIHAgPSB0cnlDYXRjaChlLCByLCBuKTsgaWYgKCJub3JtYWwiID09PSBwLnR5cGUpIHsgaWYgKG8gPSBuLmRvbmUgPyBzIDogbCwgcC5hcmcgPT09IHkpIGNvbnRpbnVlOyByZXR1cm4geyB2YWx1ZTogcC5hcmcsIGRvbmU6IG4uZG9uZSB9OyB9ICJ0aHJvdyIgPT09IHAudHlwZSAmJiAobyA9IHMsIG4ubWV0aG9kID0gInRocm93Iiwgbi5hcmcgPSBwLmFyZyk7IH0gfTsgfSBmdW5jdGlvbiBtYXliZUludm9rZURlbGVnYXRlKGUsIHIpIHsgdmFyIG4gPSByLm1ldGhvZCwgbyA9IGUuaXRlcmF0b3Jbbl07IGlmIChvID09PSB0KSByZXR1cm4gci5kZWxlZ2F0ZSA9IG51bGwsICJ0aHJvdyIgPT09IG4gJiYgZS5pdGVyYXRvclsicmV0dXJuIl0gJiYgKHIubWV0aG9kID0gInJldHVybiIsIHIuYXJnID0gdCwgbWF5YmVJbnZva2VEZWxlZ2F0ZShlLCByKSwgInRocm93IiA9PT0gci5tZXRob2QpIHx8ICJyZXR1cm4iICE9PSBuICYmIChyLm1ldGhvZCA9ICJ0aHJvdyIsIHIuYXJnID0gbmV3IFR5cGVFcnJvcigiVGhlIGl0ZXJhdG9yIGRvZXMgbm90IHByb3ZpZGUgYSAnIiArIG4gKyAiJyBtZXRob2QiKSksIHk7IHZhciBpID0gdHJ5Q2F0Y2gobywgZS5pdGVyYXRvciwgci5hcmcpOyBpZiAoInRocm93IiA9PT0gaS50eXBlKSByZXR1cm4gci5tZXRob2QgPSAidGhyb3ciLCByLmFyZyA9IGkuYXJnLCByLmRlbGVnYXRlID0gbnVsbCwgeTsgdmFyIGEgPSBpLmFyZzsgcmV0dXJuIGEgPyBhLmRvbmUgPyAocltlLnJlc3VsdE5hbWVdID0gYS52YWx1ZSwgci5uZXh0ID0gZS5uZXh0TG9jLCAicmV0dXJuIiAhPT0gci5tZXRob2QgJiYgKHIubWV0aG9kID0gIm5leHQiLCByLmFyZyA9IHQpLCByLmRlbGVnYXRlID0gbnVsbCwgeSkgOiBhIDogKHIubWV0aG9kID0gInRocm93Iiwgci5hcmcgPSBuZXcgVHlwZUVycm9yKCJpdGVyYXRvciByZXN1bHQgaXMgbm90IGFuIG9iamVjdCIpLCByLmRlbGVnYXRlID0gbnVsbCwgeSk7IH0gZnVuY3Rpb24gcHVzaFRyeUVudHJ5KHQpIHsgdmFyIGUgPSB7IHRyeUxvYzogdFswXSB9OyAxIGluIHQgJiYgKGUuY2F0Y2hMb2MgPSB0WzFdKSwgMiBpbiB0ICYmIChlLmZpbmFsbHlMb2MgPSB0WzJdLCBlLmFmdGVyTG9jID0gdFszXSksIHRoaXMudHJ5RW50cmllcy5wdXNoKGUpOyB9IGZ1bmN0aW9uIHJlc2V0VHJ5RW50cnkodCkgeyB2YXIgZSA9IHQuY29tcGxldGlvbiB8fCB7fTsgZS50eXBlID0gIm5vcm1hbCIsIGRlbGV0ZSBlLmFyZywgdC5jb21wbGV0aW9uID0gZTsgfSBmdW5jdGlvbiBDb250ZXh0KHQpIHsgdGhpcy50cnlFbnRyaWVzID0gW3sgdHJ5TG9jOiAicm9vdCIgfV0sIHQuZm9yRWFjaChwdXNoVHJ5RW50cnksIHRoaXMpLCB0aGlzLnJlc2V0KCEwKTsgfSBmdW5jdGlvbiB2YWx1ZXMoZSkgeyBpZiAoZSB8fCAiIiA9PT0gZSkgeyB2YXIgciA9IGVbYV07IGlmIChyKSByZXR1cm4gci5jYWxsKGUpOyBpZiAoImZ1bmN0aW9uIiA9PSB0eXBlb2YgZS5uZXh0KSByZXR1cm4gZTsgaWYgKCFpc05hTihlLmxlbmd0aCkpIHsgdmFyIG8gPSAtMSwgaSA9IGZ1bmN0aW9uIG5leHQoKSB7IGZvciAoOyArK28gPCBlLmxlbmd0aDspIGlmIChuLmNhbGwoZSwgbykpIHJldHVybiBuZXh0LnZhbHVlID0gZVtvXSwgbmV4dC5kb25lID0gITEsIG5leHQ7IHJldHVybiBuZXh0LnZhbHVlID0gdCwgbmV4dC5kb25lID0gITAsIG5leHQ7IH07IHJldHVybiBpLm5leHQgPSBpOyB9IH0gdGhyb3cgbmV3IFR5cGVFcnJvcihfdHlwZW9mKGUpICsgIiBpcyBub3QgaXRlcmFibGUiKTsgfSByZXR1cm4gR2VuZXJhdG9yRnVuY3Rpb24ucHJvdG90eXBlID0gR2VuZXJhdG9yRnVuY3Rpb25Qcm90b3R5cGUsIG8oZywgImNvbnN0cnVjdG9yIiwgeyB2YWx1ZTogR2VuZXJhdG9yRnVuY3Rpb25Qcm90b3R5cGUsIGNvbmZpZ3VyYWJsZTogITAgfSksIG8oR2VuZXJhdG9yRnVuY3Rpb25Qcm90b3R5cGUsICJjb25zdHJ1Y3RvciIsIHsgdmFsdWU6IEdlbmVyYXRvckZ1bmN0aW9uLCBjb25maWd1cmFibGU6ICEwIH0pLCBHZW5lcmF0b3JGdW5jdGlvbi5kaXNwbGF5TmFtZSA9IGRlZmluZShHZW5lcmF0b3JGdW5jdGlvblByb3RvdHlwZSwgdSwgIkdlbmVyYXRvckZ1bmN0aW9uIiksIGUuaXNHZW5lcmF0b3JGdW5jdGlvbiA9IGZ1bmN0aW9uICh0KSB7IHZhciBlID0gImZ1bmN0aW9uIiA9PSB0eXBlb2YgdCAmJiB0LmNvbnN0cnVjdG9yOyByZXR1cm4gISFlICYmIChlID09PSBHZW5lcmF0b3JGdW5jdGlvbiB8fCAiR2VuZXJhdG9yRnVuY3Rpb24iID09PSAoZS5kaXNwbGF5TmFtZSB8fCBlLm5hbWUpKTsgfSwgZS5tYXJrID0gZnVuY3Rpb24gKHQpIHsgcmV0dXJuIE9iamVjdC5zZXRQcm90b3R5cGVPZiA/IE9iamVjdC5zZXRQcm90b3R5cGVPZih0LCBHZW5lcmF0b3JGdW5jdGlvblByb3RvdHlwZSkgOiAodC5fX3Byb3RvX18gPSBHZW5lcmF0b3JGdW5jdGlvblByb3RvdHlwZSwgZGVmaW5lKHQsIHUsICJHZW5lcmF0b3JGdW5jdGlvbiIpKSwgdC5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKGcpLCB0OyB9LCBlLmF3cmFwID0gZnVuY3Rpb24gKHQpIHsgcmV0dXJuIHsgX19hd2FpdDogdCB9OyB9LCBkZWZpbmVJdGVyYXRvck1ldGhvZHMoQXN5bmNJdGVyYXRvci5wcm90b3R5cGUpLCBkZWZpbmUoQXN5bmNJdGVyYXRvci5wcm90b3R5cGUsIGMsIGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRoaXM7IH0pLCBlLkFzeW5jSXRlcmF0b3IgPSBBc3luY0l0ZXJhdG9yLCBlLmFzeW5jID0gZnVuY3Rpb24gKHQsIHIsIG4sIG8sIGkpIHsgdm9pZCAwID09PSBpICYmIChpID0gUHJvbWlzZSk7IHZhciBhID0gbmV3IEFzeW5jSXRlcmF0b3Iod3JhcCh0LCByLCBuLCBvKSwgaSk7IHJldHVybiBlLmlzR2VuZXJhdG9yRnVuY3Rpb24ocikgPyBhIDogYS5uZXh0KCkudGhlbihmdW5jdGlvbiAodCkgeyByZXR1cm4gdC5kb25lID8gdC52YWx1ZSA6IGEubmV4dCgpOyB9KTsgfSwgZGVmaW5lSXRlcmF0b3JNZXRob2RzKGcpLCBkZWZpbmUoZywgdSwgIkdlbmVyYXRvciIpLCBkZWZpbmUoZywgYSwgZnVuY3Rpb24gKCkgeyByZXR1cm4gdGhpczsgfSksIGRlZmluZShnLCAidG9TdHJpbmciLCBmdW5jdGlvbiAoKSB7IHJldHVybiAiW29iamVjdCBHZW5lcmF0b3JdIjsgfSksIGUua2V5cyA9IGZ1bmN0aW9uICh0KSB7IHZhciBlID0gT2JqZWN0KHQpLCByID0gW107IGZvciAodmFyIG4gaW4gZSkgci5wdXNoKG4pOyByZXR1cm4gci5yZXZlcnNlKCksIGZ1bmN0aW9uIG5leHQoKSB7IGZvciAoOyByLmxlbmd0aDspIHsgdmFyIHQgPSByLnBvcCgpOyBpZiAodCBpbiBlKSByZXR1cm4gbmV4dC52YWx1ZSA9IHQsIG5leHQuZG9uZSA9ICExLCBuZXh0OyB9IHJldHVybiBuZXh0LmRvbmUgPSAhMCwgbmV4dDsgfTsgfSwgZS52YWx1ZXMgPSB2YWx1ZXMsIENvbnRleHQucHJvdG90eXBlID0geyBjb25zdHJ1Y3RvcjogQ29udGV4dCwgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KGUpIHsgaWYgKHRoaXMucHJldiA9IDAsIHRoaXMubmV4dCA9IDAsIHRoaXMuc2VudCA9IHRoaXMuX3NlbnQgPSB0LCB0aGlzLmRvbmUgPSAhMSwgdGhpcy5kZWxlZ2F0ZSA9IG51bGwsIHRoaXMubWV0aG9kID0gIm5leHQiLCB0aGlzLmFyZyA9IHQsIHRoaXMudHJ5RW50cmllcy5mb3JFYWNoKHJlc2V0VHJ5RW50cnkpLCAhZSkgZm9yICh2YXIgciBpbiB0aGlzKSAidCIgPT09IHIuY2hhckF0KDApICYmIG4uY2FsbCh0aGlzLCByKSAmJiAhaXNOYU4oK3Iuc2xpY2UoMSkpICYmICh0aGlzW3JdID0gdCk7IH0sIHN0b3A6IGZ1bmN0aW9uIHN0b3AoKSB7IHRoaXMuZG9uZSA9ICEwOyB2YXIgdCA9IHRoaXMudHJ5RW50cmllc1swXS5jb21wbGV0aW9uOyBpZiAoInRocm93IiA9PT0gdC50eXBlKSB0aHJvdyB0LmFyZzsgcmV0dXJuIHRoaXMucnZhbDsgfSwgZGlzcGF0Y2hFeGNlcHRpb246IGZ1bmN0aW9uIGRpc3BhdGNoRXhjZXB0aW9uKGUpIHsgaWYgKHRoaXMuZG9uZSkgdGhyb3cgZTsgdmFyIHIgPSB0aGlzOyBmdW5jdGlvbiBoYW5kbGUobiwgbykgeyByZXR1cm4gYS50eXBlID0gInRocm93IiwgYS5hcmcgPSBlLCByLm5leHQgPSBuLCBvICYmIChyLm1ldGhvZCA9ICJuZXh0Iiwgci5hcmcgPSB0KSwgISFvOyB9IGZvciAodmFyIG8gPSB0aGlzLnRyeUVudHJpZXMubGVuZ3RoIC0gMTsgbyA+PSAwOyAtLW8pIHsgdmFyIGkgPSB0aGlzLnRyeUVudHJpZXNbb10sIGEgPSBpLmNvbXBsZXRpb247IGlmICgicm9vdCIgPT09IGkudHJ5TG9jKSByZXR1cm4gaGFuZGxlKCJlbmQiKTsgaWYgKGkudHJ5TG9jIDw9IHRoaXMucHJldikgeyB2YXIgYyA9IG4uY2FsbChpLCAiY2F0Y2hMb2MiKSwgdSA9IG4uY2FsbChpLCAiZmluYWxseUxvYyIpOyBpZiAoYyAmJiB1KSB7IGlmICh0aGlzLnByZXYgPCBpLmNhdGNoTG9jKSByZXR1cm4gaGFuZGxlKGkuY2F0Y2hMb2MsICEwKTsgaWYgKHRoaXMucHJldiA8IGkuZmluYWxseUxvYykgcmV0dXJuIGhhbmRsZShpLmZpbmFsbHlMb2MpOyB9IGVsc2UgaWYgKGMpIHsgaWYgKHRoaXMucHJldiA8IGkuY2F0Y2hMb2MpIHJldHVybiBoYW5kbGUoaS5jYXRjaExvYywgITApOyB9IGVsc2UgeyBpZiAoIXUpIHRocm93IG5ldyBFcnJvcigidHJ5IHN0YXRlbWVudCB3aXRob3V0IGNhdGNoIG9yIGZpbmFsbHkiKTsgaWYgKHRoaXMucHJldiA8IGkuZmluYWxseUxvYykgcmV0dXJuIGhhbmRsZShpLmZpbmFsbHlMb2MpOyB9IH0gfSB9LCBhYnJ1cHQ6IGZ1bmN0aW9uIGFicnVwdCh0LCBlKSB7IGZvciAodmFyIHIgPSB0aGlzLnRyeUVudHJpZXMubGVuZ3RoIC0gMTsgciA+PSAwOyAtLXIpIHsgdmFyIG8gPSB0aGlzLnRyeUVudHJpZXNbcl07IGlmIChvLnRyeUxvYyA8PSB0aGlzLnByZXYgJiYgbi5jYWxsKG8sICJmaW5hbGx5TG9jIikgJiYgdGhpcy5wcmV2IDwgby5maW5hbGx5TG9jKSB7IHZhciBpID0gbzsgYnJlYWs7IH0gfSBpICYmICgiYnJlYWsiID09PSB0IHx8ICJjb250aW51ZSIgPT09IHQpICYmIGkudHJ5TG9jIDw9IGUgJiYgZSA8PSBpLmZpbmFsbHlMb2MgJiYgKGkgPSBudWxsKTsgdmFyIGEgPSBpID8gaS5jb21wbGV0aW9uIDoge307IHJldHVybiBhLnR5cGUgPSB0LCBhLmFyZyA9IGUsIGkgPyAodGhpcy5tZXRob2QgPSAibmV4dCIsIHRoaXMubmV4dCA9IGkuZmluYWxseUxvYywgeSkgOiB0aGlzLmNvbXBsZXRlKGEpOyB9LCBjb21wbGV0ZTogZnVuY3Rpb24gY29tcGxldGUodCwgZSkgeyBpZiAoInRocm93IiA9PT0gdC50eXBlKSB0aHJvdyB0LmFyZzsgcmV0dXJuICJicmVhayIgPT09IHQudHlwZSB8fCAiY29udGludWUiID09PSB0LnR5cGUgPyB0aGlzLm5leHQgPSB0LmFyZyA6ICJyZXR1cm4iID09PSB0LnR5cGUgPyAodGhpcy5ydmFsID0gdGhpcy5hcmcgPSB0LmFyZywgdGhpcy5tZXRob2QgPSAicmV0dXJuIiwgdGhpcy5uZXh0ID0gImVuZCIpIDogIm5vcm1hbCIgPT09IHQudHlwZSAmJiBlICYmICh0aGlzLm5leHQgPSBlKSwgeTsgfSwgZmluaXNoOiBmdW5jdGlvbiBmaW5pc2godCkgeyBmb3IgKHZhciBlID0gdGhpcy50cnlFbnRyaWVzLmxlbmd0aCAtIDE7IGUgPj0gMDsgLS1lKSB7IHZhciByID0gdGhpcy50cnlFbnRyaWVzW2VdOyBpZiAoci5maW5hbGx5TG9jID09PSB0KSByZXR1cm4gdGhpcy5jb21wbGV0ZShyLmNvbXBsZXRpb24sIHIuYWZ0ZXJMb2MpLCByZXNldFRyeUVudHJ5KHIpLCB5OyB9IH0sICJjYXRjaCI6IGZ1bmN0aW9uIF9jYXRjaCh0KSB7IGZvciAodmFyIGUgPSB0aGlzLnRyeUVudHJpZXMubGVuZ3RoIC0gMTsgZSA+PSAwOyAtLWUpIHsgdmFyIHIgPSB0aGlzLnRyeUVudHJpZXNbZV07IGlmIChyLnRyeUxvYyA9PT0gdCkgeyB2YXIgbiA9IHIuY29tcGxldGlvbjsgaWYgKCJ0aHJvdyIgPT09IG4udHlwZSkgeyB2YXIgbyA9IG4uYXJnOyByZXNldFRyeUVudHJ5KHIpOyB9IHJldHVybiBvOyB9IH0gdGhyb3cgbmV3IEVycm9yKCJpbGxlZ2FsIGNhdGNoIGF0dGVtcHQiKTsgfSwgZGVsZWdhdGVZaWVsZDogZnVuY3Rpb24gZGVsZWdhdGVZaWVsZChlLCByLCBuKSB7IHJldHVybiB0aGlzLmRlbGVnYXRlID0geyBpdGVyYXRvcjogdmFsdWVzKGUpLCByZXN1bHROYW1lOiByLCBuZXh0TG9jOiBuIH0sICJuZXh0IiA9PT0gdGhpcy5tZXRob2QgJiYgKHRoaXMuYXJnID0gdCksIHk7IH0gfSwgZTsgfQpmdW5jdGlvbiBhc3luY0dlbmVyYXRvclN0ZXAoZ2VuLCByZXNvbHZlLCByZWplY3QsIF9uZXh0LCBfdGhyb3csIGtleSwgYXJnKSB7IHRyeSB7IHZhciBpbmZvID0gZ2VuW2tleV0oYXJnKTsgdmFyIHZhbHVlID0gaW5mby52YWx1ZTsgfSBjYXRjaCAoZXJyb3IpIHsgcmVqZWN0KGVycm9yKTsgcmV0dXJuOyB9IGlmIChpbmZvLmRvbmUpIHsgcmVzb2x2ZSh2YWx1ZSk7IH0gZWxzZSB7IFByb21pc2UucmVzb2x2ZSh2YWx1ZSkudGhlbihfbmV4dCwgX3Rocm93KTsgfSB9CmZ1bmN0aW9uIF9hc3luY1RvR2VuZXJhdG9yKGZuKSB7IHJldHVybiBmdW5jdGlvbiAoKSB7IHZhciBzZWxmID0gdGhpcywgYXJncyA9IGFyZ3VtZW50czsgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsgdmFyIGdlbiA9IGZuLmFwcGx5KHNlbGYsIGFyZ3MpOyBmdW5jdGlvbiBfbmV4dCh2YWx1ZSkgeyBhc3luY0dlbmVyYXRvclN0ZXAoZ2VuLCByZXNvbHZlLCByZWplY3QsIF9uZXh0LCBfdGhyb3csICJuZXh0IiwgdmFsdWUpOyB9IGZ1bmN0aW9uIF90aHJvdyhlcnIpIHsgYXN5bmNHZW5lcmF0b3JTdGVwKGdlbiwgcmVzb2x2ZSwgcmVqZWN0LCBfbmV4dCwgX3Rocm93LCAidGhyb3ciLCBlcnIpOyB9IF9uZXh0KHVuZGVmaW5lZCk7IH0pOyB9OyB9CmZ1bmN0aW9uIG93bktleXMoZSwgcikgeyB2YXIgdCA9IE9iamVjdC5rZXlzKGUpOyBpZiAoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scykgeyB2YXIgbyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoZSk7IHIgJiYgKG8gPSBvLmZpbHRlcihmdW5jdGlvbiAocikgeyByZXR1cm4gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlLCByKS5lbnVtZXJhYmxlOyB9KSksIHQucHVzaC5hcHBseSh0LCBvKTsgfSByZXR1cm4gdDsgfQpmdW5jdGlvbiBfb2JqZWN0U3ByZWFkKGUpIHsgZm9yICh2YXIgciA9IDE7IHIgPCBhcmd1bWVudHMubGVuZ3RoOyByKyspIHsgdmFyIHQgPSBudWxsICE9IGFyZ3VtZW50c1tyXSA/IGFyZ3VtZW50c1tyXSA6IHt9OyByICUgMiA/IG93bktleXMoT2JqZWN0KHQpLCAhMCkuZm9yRWFjaChmdW5jdGlvbiAocikgeyBfZGVmaW5lUHJvcGVydHkoZSwgciwgdFtyXSk7IH0pIDogT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnMgPyBPYmplY3QuZGVmaW5lUHJvcGVydGllcyhlLCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyh0KSkgOiBvd25LZXlzKE9iamVjdCh0KSkuZm9yRWFjaChmdW5jdGlvbiAocikgeyBPYmplY3QuZGVmaW5lUHJvcGVydHkoZSwgciwgT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcih0LCByKSk7IH0pOyB9IHJldHVybiBlOyB9CmZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0eShvYmosIGtleSwgdmFsdWUpIHsga2V5ID0gX3RvUHJvcGVydHlLZXkoa2V5KTsgaWYgKGtleSBpbiBvYmopIHsgT2JqZWN0LmRlZmluZVByb3BlcnR5KG9iaiwga2V5LCB7IHZhbHVlOiB2YWx1ZSwgZW51bWVyYWJsZTogdHJ1ZSwgY29uZmlndXJhYmxlOiB0cnVlLCB3cml0YWJsZTogdHJ1ZSB9KTsgfSBlbHNlIHsgb2JqW2tleV0gPSB2YWx1ZTsgfSByZXR1cm4gb2JqOyB9CmZ1bmN0aW9uIF90b1Byb3BlcnR5S2V5KHQpIHsgdmFyIGkgPSBfdG9QcmltaXRpdmUodCwgInN0cmluZyIpOyByZXR1cm4gInN5bWJvbCIgPT0gX3R5cGVvZihpKSA/IGkgOiBTdHJpbmcoaSk7IH0KZnVuY3Rpb24gX3RvUHJpbWl0aXZlKHQsIHIpIHsgaWYgKCJvYmplY3QiICE9IF90eXBlb2YodCkgfHwgIXQpIHJldHVybiB0OyB2YXIgZSA9IHRbU3ltYm9sLnRvUHJpbWl0aXZlXTsgaWYgKHZvaWQgMCAhPT0gZSkgeyB2YXIgaSA9IGUuY2FsbCh0LCByIHx8ICJkZWZhdWx0Iik7IGlmICgib2JqZWN0IiAhPSBfdHlwZW9mKGkpKSByZXR1cm4gaTsgdGhyb3cgbmV3IFR5cGVFcnJvcigiQEB0b1ByaW1pdGl2ZSBtdXN0IHJldHVybiBhIHByaW1pdGl2ZSB2YWx1ZS4iKTsgfSByZXR1cm4gKCJzdHJpbmciID09PSByID8gU3RyaW5nIDogTnVtYmVyKSh0KTsgfSAvLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzWyJkZWZhdWx0Il0gPSB7CiAgY29tcG9uZW50czoge30sCiAgcHJvcHM6IHt9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0cmFja0RhdGE6IFtdLAogICAgICB0aXRsZUxpc3Q6IFtdLAogICAgICBzZWN0aW9uSW5mb0xpc3Q6IFtdLAogICAgICBzZWxlY3RlZEluZGV4OiAwLAogICAgICAvLyDorrDlvZXlvZPliY3pgInkuK3nmoTpobkKICAgICAgc2VsZWN0ZWRJdGVtOiB7fSwKICAgICAgLy8g6K6w5b2V5b2T5YmN6YCJ5Lit55qE6aG5CiAgICAgIGlzdXNlUG9wdXA6IGZhbHNlLAogICAgICBwb3B1cHRpdGxlOiAnJywKICAgICAgdW5oaXJlaXRlbToge30sCiAgICAgIHZlcnNpb25Qb3B1cDogZmFsc2UsCiAgICAgIC8vICAgY29sdW1uczpbJzEuMCcsJzEuMScsJzEuMiddLAogICAgICBjb2x1bW5zOiBbewogICAgICAgIHRleHQ6ICcxLjAnLAogICAgICAgIHZhbHVlOiAnMS4wJwogICAgICB9LCB7CiAgICAgICAgdGV4dDogJzEuMScsCiAgICAgICAgdmFsdWU6ICcxLjEnCiAgICAgIH0sIHsKICAgICAgICB0ZXh0OiAnMS4yJywKICAgICAgICB2YWx1ZTogJzEuMicKICAgICAgfV0sCiAgICAgIGlzV2VDaGF0OiBmYWxzZQogICAgfTsKICB9LAogIGNvbXB1dGVkOiBfb2JqZWN0U3ByZWFkKHt9LCAoMCwgX3Z1ZXgubWFwR2V0dGVycykoWyJzY2FuQ29kZUluZm8iXSkpLAogIHdhdGNoOiB7fSwKICAvL+aWueazlembhuWQiAogIG1ldGhvZHM6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCAoMCwgX3Z1ZXgubWFwQWN0aW9ucykoInJlcGFpcl9tb2R1bGVzL3JlcGFpciIsIFsiZ2V0Q29uZmlnSW5mb09uZSIsICJjYXRlZ29yeUxpc3QiLCAibnVtYmVyVmVyc2lvbkxpc3QiLCAiYmluZE9yVW5iaW5kIl0pKSwgKDAsIF92dWV4Lm1hcE11dGF0aW9ucykoInJlcGFpcl9tb2R1bGVzL3JlcGFpciIsIFsic2V0U2NhbkNvZGUiXSkpLCB7fSwgewogICAgb25DbGlja0xlZnQ6IGZ1bmN0aW9uIG9uQ2xpY2tMZWZ0KCkgewogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfSwKICAgIHNlbGVjdEl0ZW06IGZ1bmN0aW9uIHNlbGVjdEl0ZW0oaW5kZXgsIGl0ZW0pIHsKICAgICAgY29uc29sZS5sb2coaXRlbSk7CiAgICAgIC8vIHRoaXMuc2VsZWN0ZWRJdGVtPWl0ZW0KICAgICAgdGhpcy5wb3B1cHRpdGxlID0gaXRlbS50cmFja0NpcmN1aXRDb25maWcuZHJhd2luZ05hbWU7CiAgICAgIC8vIOWmguaenOeCueWHu+eahOaYr+W3sumAieS4reeahOmhue+8jOWImeWPlua2iOmAieaLqe+8jOWQpuWImemAieS4reivpemhuQogICAgICB0aGlzLnNlbGVjdGVkSW5kZXggPSB0aGlzLnNlbGVjdGVkSW5kZXggPT09IGluZGV4ID8gbnVsbCA6IGluZGV4OwogICAgICB0aGlzLmdldGRhdGEoaXRlbS50cmFja0NpcmN1aXRDb25maWcuaWQpOwogICAgfSwKICAgIGdldGNhdGVnb3J5TGlzdDogZnVuY3Rpb24gZ2V0Y2F0ZWdvcnlMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciByZXM7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzLmNhdGVnb3J5TGlzdCh7CiAgICAgICAgICAgICAgICBzdGF0aW9uSWQ6IF90aGlzLiRyb3V0ZS5xdWVyeS5zdGF0aW9uSWQKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDApIHsKICAgICAgICAgICAgICAgIF90aGlzLnRyYWNrRGF0YSA9IF90aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09IDEgPyByZXMuZGF0YVswXSA6IHJlcy5kYXRhWzFdOwogICAgICAgICAgICAgICAgX3RoaXMucG9wdXB0aXRsZSA9IF90aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09IDEgPyByZXMuZGF0YVswXS5jaGlsZHJlblswXS50cmFja0NpcmN1aXRDb25maWcuZHJhd2luZ05hbWUgOiByZXMuZGF0YVsxXS5jaGlsZHJlblswXS50cmFja0NpcmN1aXRDb25maWcuZHJhd2luZ05hbWU7CiAgICAgICAgICAgICAgICBpZiAoX3RoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUgPT0gMSkgewogICAgICAgICAgICAgICAgICBfdGhpcy5nZXRkYXRhKF90aGlzLnRyYWNrRGF0YS5jaGlsZHJlblswXS50cmFja0NpcmN1aXRDb25maWcuaWQpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMuZ2V0ZGF0YShfdGhpcy50cmFja0RhdGEuY2hpbGRyZW5bMF0udHJhY2tDaXJjdWl0Q29uZmlnLmlkKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBnZXRkYXRhOiBmdW5jdGlvbiBnZXRkYXRhKGNhdGVnb3J5SWQpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHZhciByZXM7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLmdldENvbmZpZ0luZm9PbmUoewogICAgICAgICAgICAgICAgY29uZmlnSWQ6IGNhdGVnb3J5SWQsCiAgICAgICAgICAgICAgICBzdGF0aW9uSWQ6IF90aGlzMi4kcm91dGUucXVlcnkuc3RhdGlvbklkLAogICAgICAgICAgICAgICAgY2F0ZWdvcnlJZDogX3RoaXMyLiRyb3V0ZS5xdWVyeS5jYXRlZ29yeUlkCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDIuc2VudDsKICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gMCkgewogICAgICAgICAgICAgICAgX3RoaXMyLnRpdGxlTGlzdCA9IHJlcy5kYXRhLnNlY3Rpb25JbmZvTGlzdFswXS5jb25maWdJbmZvTGlzdDsKICAgICAgICAgICAgICAgIF90aGlzMi5zZWN0aW9uSW5mb0xpc3QgPSByZXMuZGF0YS5zZWN0aW9uSW5mb0xpc3Q7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDliKTmlq3mmK/lkKblnKjlvq7kv6Hnjq/looMKICAgIGlzTWluaVByb2dyYW06IGZ1bmN0aW9uIGlzTWluaVByb2dyYW0oY2FsbGJhY2spIHsKICAgICAgdmFyIHVhID0gd2luZG93Lm5hdmlnYXRvci51c2VyQWdlbnQudG9Mb3dlckNhc2UoKTsKICAgICAgaWYgKHVhLm1hdGNoKC9NaWNyb01lc3Nlbmdlci9pKSAhPSAibWljcm9tZXNzZW5nZXIiKSB7CiAgICAgICAgY2FsbGJhY2soZmFsc2UpOwogICAgICB9IGVsc2UgewogICAgICAgIF93ZWl4aW5Kc1Nka1siZGVmYXVsdCJdLm1pbmlQcm9ncmFtLmdldEVudihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLm1pbmlwcm9ncmFtKSB7CiAgICAgICAgICAgIGNhbGxiYWNrKHRydWUpOyAvL+Wwj+eoi+W6j+eOr+WigwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY2FsbGJhY2soZmFsc2UpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgY2hlY2tUeXBlRnVuOiBmdW5jdGlvbiBjaGVja1R5cGVGdW4oaXRlbSwgdmFsdWUpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAodmFsdWUuaXNVc2UpIHsKICAgICAgICAgICAgICAgIF90aGlzMy51bmhpcmUodmFsdWUpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnc2VjdGlvbkluZm9JdGVtJywgSlNPTi5zdHJpbmdpZnkodmFsdWUpKTsKICAgICAgICAgICAgICAgIF90aGlzMy5pc01pbmlQcm9ncmFtKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgICAgaWYgKHJlcykgewogICAgICAgICAgICAgICAgICAgIF90aGlzMy5pc1dlQ2hhdCA9IHRydWU7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBwYXRoOiAnL3JlcGFpci9zY2FuQ29kZScsCiAgICAgICAgICAgICAgICAgICAgICBxdWVyeTogewogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnY291cmllckNvZGUnCiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMzLmlzV2VDaGF0ID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBwYXRoOiAndHJhY2tDaXJjdWl0L3FyY29kZScKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgLy8gICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgICAgICAgICAgLy8gICAgIHBhdGg6Jy9yZXBhaXIvc2NhbkNvZGUnLAogICAgICAgICAgICAgICAgLy8gICAgIHF1ZXJ5OnsKICAgICAgICAgICAgICAgIC8vICAgICAgICAgdHlwZTonY291cmllckNvZGUnCiAgICAgICAgICAgICAgICAvLyAgICAgfQogICAgICAgICAgICAgICAgLy8gICAgfSkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMyk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGNsb3NlUG9wdXA6IGZ1bmN0aW9uIGNsb3NlUG9wdXAoKSB7CiAgICAgIHRoaXMuaXN1c2VQb3B1cCA9IGZhbHNlOwogICAgICB0aGlzLnBvcHVwdGl0bGUgPSAnJzsKICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3NlY3Rpb25JbmZvSXRlbScpOwogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY3NxcmNvZGUnKTsKICAgIH0sCiAgICB1bmhpcmU6IGZ1bmN0aW9uIHVuaGlyZSh2YWx1ZSkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIHJlczsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29uc29sZS5sb2codmFsdWUpOwogICAgICAgICAgICAgIF90aGlzNC5pc3VzZVBvcHVwID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNC5udW1iZXJWZXJzaW9uTGlzdCh7CiAgICAgICAgICAgICAgICBzZWN0aW9uSW5mb0lkOiB2YWx1ZS5pZAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQ0LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDApIHsKICAgICAgICAgICAgICAgIF90aGlzNC51bmhpcmVpdGVtID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzNC4kdG9hc3QuZmFpbChyZXMubWVnKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIG5ld3VuaGlyZTogZnVuY3Rpb24gbmV3dW5oaXJlKHZhbHVlKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICB2YXIgcmVzLCBfdGhpczUkc2NhbkNvZGVJbmZvLCBzY2FuQ29kZSwgY29kZTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTUkKF9jb250ZXh0NSkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ1LnByZXYgPSBfY29udGV4dDUubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29uc29sZS5sb2codmFsdWUpOwogICAgICAgICAgICAgIF90aGlzNS5pc3VzZVBvcHVwID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNS5udW1iZXJWZXJzaW9uTGlzdCh7CiAgICAgICAgICAgICAgICBzZWN0aW9uSW5mb0lkOiBKU09OLnBhcnNlKHZhbHVlKS5pZAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQ1LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDApIHsKICAgICAgICAgICAgICAgIF90aGlzNS51bmhpcmVpdGVtID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgICBpZiAoX3RoaXM1LmlzV2VDaGF0KSB7CiAgICAgICAgICAgICAgICAgIHNjYW5Db2RlID0gKChfdGhpczUkc2NhbkNvZGVJbmZvID0gX3RoaXM1LnNjYW5Db2RlSW5mbykgPT09IG51bGwgfHwgX3RoaXM1JHNjYW5Db2RlSW5mbyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXM1JHNjYW5Db2RlSW5mby5jb3VyaWVyQ29kZSkgfHwgIiI7CiAgICAgICAgICAgICAgICAgIF90aGlzNS51bmhpcmVpdGVtLm51bWJlciA9IHNjYW5Db2RlOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgY29kZSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2NzcXJjb2RlJykpOwogICAgICAgICAgICAgICAgICBfdGhpczUudW5oaXJlaXRlbS5udW1iZXIgPSBjb2RlLmRlY29kZWRUZXh0OwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczUuJHRvYXN0LmZhaWwocmVzLm1lZyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICB1bmJpbmQ6IGZ1bmN0aW9uIHVuYmluZCgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU3KCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNyQoX2NvbnRleHQ3KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDcucHJldiA9IF9jb250ZXh0Ny5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfdGhpczYuJGRpYWxvZy5jb25maXJtKHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICJcdTY2MkZcdTU0MjZcdTg5ODEiLmNvbmNhdChfdGhpczYudW5oaXJlaXRlbS5pc1VzZSA/ICfop6Pnu5EnIDogJ+e7keWumicpLAogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICJcdTc4NkVcdThCQTQiLmNvbmNhdChfdGhpczYudW5oaXJlaXRlbS5pc1VzZSA/ICfop6Pnu5EnIDogJ+e7keWumicpCiAgICAgICAgICAgICAgfSkudGhlbiggLyojX19QVVJFX18qL19hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTYoKSB7CiAgICAgICAgICAgICAgICB2YXIgb2JqLCByZXM7CiAgICAgICAgICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTYkKF9jb250ZXh0NikgewogICAgICAgICAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDYucHJldiA9IF9jb250ZXh0Ni5uZXh0KSB7CiAgICAgICAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgICAgICAgb2JqID0gewogICAgICAgICAgICAgICAgICAgICAgICBpZDogX3RoaXM2LnVuaGlyZWl0ZW0uaWQsCiAgICAgICAgICAgICAgICAgICAgICAgIGlzVXNlOiBfdGhpczYudW5oaXJlaXRlbS5pc1VzZSA/IGZhbHNlIDogdHJ1ZQogICAgICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgICAgIGlmICghX3RoaXM2LnVuaGlyZWl0ZW0uaXNVc2UpIHsKICAgICAgICAgICAgICAgICAgICAgICAgb2JqLm51bWJlciA9IF90aGlzNi51bmhpcmVpdGVtLm51bWJlcjsKICAgICAgICAgICAgICAgICAgICAgICAgb2JqLnZlcnNpb24gPSBfdGhpczYudW5oaXJlaXRlbS52ZXJzaW9uOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSA0OwogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNi5iaW5kT3JVbmJpbmQob2JqKTsKICAgICAgICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDYuc2VudDsKICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAwKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNi5zZXRTY2FuQ29kZSh7fSk7CiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzNi4kdG9hc3Quc3VjY2VzcygiIi5jb25jYXQoX3RoaXM2LnVuaGlyZWl0ZW0uaXNVc2UgPyAn6Kej57uRJyA6ICfnu5HlrponLCAiXHU2MjEwXHU1MjlGXHVGRjAxIikpOwogICAgICAgICAgICAgICAgICAgICAgICBfdGhpczYuY2xvc2VQb3B1cCgpOwogICAgICAgICAgICAgICAgICAgICAgICBfdGhpczYuZ2V0Y2F0ZWdvcnlMaXN0KCk7CiAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICBfdGhpczYuJHRvYXN0LmZhaWwocmVzLm1zZyB8fCAn5pON5L2c5aSx6LSlJyk7CiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ2LnN0b3AoKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSwgX2NhbGxlZTYpOwogICAgICAgICAgICAgIH0pKSk7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ3LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNyk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIG9uQ29uZmlybTogZnVuY3Rpb24gb25Db25maXJtKHZhbHVlKSB7CiAgICAgIHRoaXMudW5oaXJlaXRlbS52ZXJzaW9uID0gdmFsdWUudGV4dDsKICAgICAgdGhpcy52ZXJzaW9uUG9wdXAgPSBmYWxzZTsKICAgIH0sCiAgICBvbkNhbmNlbDogZnVuY3Rpb24gb25DYW5jZWwoKSB7CiAgICAgIHRoaXMudmVyc2lvblBvcHVwID0gZmFsc2U7CiAgICB9LAogICAgY2xvc2V2ZXJzaW9uUG9wdXA6IGZ1bmN0aW9uIGNsb3NldmVyc2lvblBvcHVwKCkgewogICAgICB0aGlzLnZlcnNpb25Qb3B1cCA9IGZhbHNlOwogICAgfQogIH0pLAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU4KCkgewogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTgkKF9jb250ZXh0OCkgewogICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OC5wcmV2ID0gX2NvbnRleHQ4Lm5leHQpIHsKICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgX3RoaXM3LmlzTWluaVByb2dyYW0oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgIGlmIChyZXMpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJpc01pbmlQcm9ncmFtIik7CiAgICAgICAgICAgICAgICBfdGhpczcuaXNXZUNoYXQgPSB0cnVlOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczcuaXNXZUNoYXQgPSBmYWxzZTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgY2FzZSAxOgogICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5zdG9wKCk7CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlOCk7CiAgICB9KSkoKTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICBjb25zb2xlLmxvZyhsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY3NxcmNvZGUnKSk7CiAgICBpZiAobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3NlY3Rpb25JbmZvSXRlbScpKSB7CiAgICAgIHZhciBpdGVtID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3NlY3Rpb25JbmZvSXRlbScpOwogICAgICBpZiAodGhpcy5zY2FuQ29kZUluZm8uY291cmllckNvZGUgfHwgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2NzcXJjb2RlJykpIHsKICAgICAgICB0aGlzLm5ld3VuaGlyZShpdGVtKTsKICAgICAgfQogICAgfQogICAgdGhpcy5nZXRjYXRlZ29yeUxpc3QoKTsKICB9Cn07"}, null]}