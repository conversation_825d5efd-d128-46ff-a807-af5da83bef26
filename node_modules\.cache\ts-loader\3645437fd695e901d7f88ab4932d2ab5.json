{"remainingRequest": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js!E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js!E:\\smallProgram\\node_modules\\ts-loader\\index.js??ref--13-3!E:\\smallProgram\\src\\api\\sign-controller.service.ts", "dependencies": [{"path": "E:\\smallProgram\\src\\api\\sign-controller.service.ts", "mtime": 1755501752465}, {"path": "E:\\smallProgram\\babel.config.js", "mtime": 1753929329699}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751509197733}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\ts-loader\\index.js", "mtime": 1751509198841}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLnNpZ25TZXJ2aWNlID0gdm9pZCAwOwp2YXIgX3V0aWxzID0gcmVxdWlyZSgiQC91dGlscyIpOwpmdW5jdGlvbiBfdHlwZW9mKG8pIHsgIkBiYWJlbC9oZWxwZXJzIC0gdHlwZW9mIjsgcmV0dXJuIF90eXBlb2YgPSAiZnVuY3Rpb24iID09IHR5cGVvZiBTeW1ib2wgJiYgInN5bWJvbCIgPT0gdHlwZW9mIFN5bWJvbC5pdGVyYXRvciA/IGZ1bmN0aW9uIChvKSB7IHJldHVybiB0eXBlb2YgbzsgfSA6IGZ1bmN0aW9uIChvKSB7IHJldHVybiBvICYmICJmdW5jdGlvbiIgPT0gdHlwZW9mIFN5bWJvbCAmJiBvLmNvbnN0cnVjdG9yID09PSBTeW1ib2wgJiYgbyAhPT0gU3ltYm9sLnByb3RvdHlwZSA/ICJzeW1ib2wiIDogdHlwZW9mIG87IH0sIF90eXBlb2Yobyk7IH0KZnVuY3Rpb24gX2NsYXNzQ2FsbENoZWNrKGluc3RhbmNlLCBDb25zdHJ1Y3RvcikgeyBpZiAoIShpbnN0YW5jZSBpbnN0YW5jZW9mIENvbnN0cnVjdG9yKSkgeyB0aHJvdyBuZXcgVHlwZUVycm9yKCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb24iKTsgfSB9CmZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0aWVzKHRhcmdldCwgcHJvcHMpIHsgZm9yICh2YXIgaSA9IDA7IGkgPCBwcm9wcy5sZW5ndGg7IGkrKykgeyB2YXIgZGVzY3JpcHRvciA9IHByb3BzW2ldOyBkZXNjcmlwdG9yLmVudW1lcmFibGUgPSBkZXNjcmlwdG9yLmVudW1lcmFibGUgfHwgZmFsc2U7IGRlc2NyaXB0b3IuY29uZmlndXJhYmxlID0gdHJ1ZTsgaWYgKCJ2YWx1ZSIgaW4gZGVzY3JpcHRvcikgZGVzY3JpcHRvci53cml0YWJsZSA9IHRydWU7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIF90b1Byb3BlcnR5S2V5KGRlc2NyaXB0b3Iua2V5KSwgZGVzY3JpcHRvcik7IH0gfQpmdW5jdGlvbiBfY3JlYXRlQ2xhc3MoQ29uc3RydWN0b3IsIHByb3RvUHJvcHMsIHN0YXRpY1Byb3BzKSB7IGlmIChwcm90b1Byb3BzKSBfZGVmaW5lUHJvcGVydGllcyhDb25zdHJ1Y3Rvci5wcm90b3R5cGUsIHByb3RvUHJvcHMpOyBpZiAoc3RhdGljUHJvcHMpIF9kZWZpbmVQcm9wZXJ0aWVzKENvbnN0cnVjdG9yLCBzdGF0aWNQcm9wcyk7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShDb25zdHJ1Y3RvciwgInByb3RvdHlwZSIsIHsgd3JpdGFibGU6IGZhbHNlIH0pOyByZXR1cm4gQ29uc3RydWN0b3I7IH0KZnVuY3Rpb24gX3RvUHJvcGVydHlLZXkodCkgeyB2YXIgaSA9IF90b1ByaW1pdGl2ZSh0LCAic3RyaW5nIik7IHJldHVybiAic3ltYm9sIiA9PSBfdHlwZW9mKGkpID8gaSA6IFN0cmluZyhpKTsgfQpmdW5jdGlvbiBfdG9QcmltaXRpdmUodCwgcikgeyBpZiAoIm9iamVjdCIgIT0gX3R5cGVvZih0KSB8fCAhdCkgcmV0dXJuIHQ7IHZhciBlID0gdFtTeW1ib2wudG9QcmltaXRpdmVdOyBpZiAodm9pZCAwICE9PSBlKSB7IHZhciBpID0gZS5jYWxsKHQsIHIgfHwgImRlZmF1bHQiKTsgaWYgKCJvYmplY3QiICE9IF90eXBlb2YoaSkpIHJldHVybiBpOyB0aHJvdyBuZXcgVHlwZUVycm9yKCJAQHRvUHJpbWl0aXZlIG11c3QgcmV0dXJuIGEgcHJpbWl0aXZlIHZhbHVlLiIpOyB9IHJldHVybiAoInN0cmluZyIgPT09IHIgPyBTdHJpbmcgOiBOdW1iZXIpKHQpOyB9CnZhciBzaWduID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHsKICBmdW5jdGlvbiBzaWduKCkgewogICAgX2NsYXNzQ2FsbENoZWNrKHRoaXMsIHNpZ24pOwogIH0KICBfY3JlYXRlQ2xhc3Moc2lnbiwgW3sKICAgIGtleTogInNpZ25Eb2N1bWVudCIsCiAgICB2YWx1ZToKICAgIC8v562+572y5paH5qGjIC0g5p+l55yL5oiR55qE5paH5Lu2CiAgICBmdW5jdGlvbiBzaWduRG9jdW1lbnQocGF5bG9hZCkgewogICAgICByZXR1cm4gKDAsIF91dGlscy5yZXF1ZXN0KSh7CiAgICAgICAgdXJsOiAiL3Byb2R1Y3RzaWduL3NpZ25Eb2N1bWVudCIsCiAgICAgICAgbWV0aG9kOiAicG9zdCIsCiAgICAgICAgcGFyYW1zOiBwYXlsb2FkCiAgICAgIH0pOwogICAgfQogICAgLy/nrb7nvbLmlofmoaMgLSDnrb7nvbLpk77mjqUKICB9LCB7CiAgICBrZXk6ICJkb3NpZ25GZWlnbkRpbmdUYWxrIiwKICAgIHZhbHVlOiBmdW5jdGlvbiBkb3NpZ25GZWlnbkRpbmdUYWxrKHBheWxvYWQpIHsKICAgICAgcmV0dXJuICgwLCBfdXRpbHMucmVxdWVzdCkoewogICAgICAgIHVybDogIi9wcm9kdWN0c2lnbi9kb3NpZ25GZWlnbkRpbmdUYWxrIiwKICAgICAgICBtZXRob2Q6ICJwb3N0IiwKICAgICAgICBwYXJhbXM6IHBheWxvYWQKICAgICAgfSk7CiAgICB9CiAgfV0pOwogIHJldHVybiBzaWduOwp9KCk7CnZhciBzaWduU2VydmljZSA9IGV4cG9ydHMuc2lnblNlcnZpY2UgPSBuZXcgc2lnbigpOw=="}, null]}