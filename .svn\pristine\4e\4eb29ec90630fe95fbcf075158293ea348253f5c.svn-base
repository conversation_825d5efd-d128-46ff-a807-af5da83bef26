import { signService } from "@/api/sign-controller.service";
import { Message } from "element-ui";
import { tokenGuard } from "@/utils";
const message = Message;

const mutations = {
    
};

const actions = {
  //签署文档 - 查看我的文件
  async signDocument({ commit }: any, payload: any) {
    try {
      const data = await signService.signDocument(payload);
      return data;
    } catch (error) {
      console.log(error);
    }
  },
  //签署文档 - 签署链接
  async dosignFeignDingTalk({ commit }: any, payload: any) {
    try {
      const data = await signService.dosignFeignDingTalk(payload);
      return data;
    } catch (error) {
      console.log(error);
    }
  },

}

export default {
  namespaced: true,
  mutations,
  actions,
};