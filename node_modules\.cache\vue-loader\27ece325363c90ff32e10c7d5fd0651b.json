{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\singleProduct\\mobileView.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\singleProduct\\mobileView.vue", "mtime": 1751451780502}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJtb2JpbGVWaWV3SDUiLA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4gew0KICAgIH0NCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["mobileView.vue"], "names": [], "mappings": ";;;;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "mobileView.vue", "sourceRoot": "src/views/singleProduct", "sourcesContent": ["<template>\r\n  <div class=\"drlxm\">\r\n    <div class=\"flex_center boxBorder\">\r\n      <img src=\"@/assets/icon.png\" alt=\"\">\r\n      <div class=\"title1\">请到手机端钉钉待办中处理</div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: \"mobileViewH5\",\r\n  data () {\r\n    return {\r\n    }\r\n  },\r\n};\r\n</script>\r\n <style scoped lang=\"less\">\r\n.drlxm {\r\n  background: #fff;\r\n  height: 100vh;\r\n  padding: 14vh 25px 0 25px;\r\n}\r\n.boxBorder {\r\n  flex-direction: column;\r\n  border: 1px solid #ccc;\r\n  padding: 15px 0;\r\n  border-radius: 10px;\r\n}\r\nimg {\r\n  width: 60px;\r\n  height: 60px;\r\n}\r\n.title1 {\r\n  margin: 15px 0 10px 0;\r\n  font-size: 14px;\r\n  color: #878787;\r\n}\r\n</style>\r\n\r\n"]}]}