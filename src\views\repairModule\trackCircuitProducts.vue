<template>
  <div class='box'>
    <!-- <van-nav-bar
      :title='$route.query.categoryName'
      left-arrow
      @click-left="onClickLeft"
    /> -->

    <div class="header">
      <div 
        class="header-title" 
        v-for="(item,index) in trackData.children" 
        :key="index"
        :class="{ active: selectedIndex === index }"
        @click="selectItem(index,item)"
      >
        {{ item.trackCircuitConfig.drawingName }}
      </div>
    </div>
    <!--移动柜/电码化发送柜  -->
    <div class="content" v-if="$route.query.type==1">
        <div class="content-box">
            <div class="content-left">
                <div></div>
                <div class="content-title" v-for="item in titleList">
                {{ item.configName}}:
                </div>
                <div></div>
                <div class="content-title" v-for="item in titleList">
                {{ item.configName}}:
                </div>
            </div>
            
            <div class="content-list">
                <div class="item" v-for="item in sectionInfoList">
                    <div class="item-title">
                        {{ item.sectionName}}
                    </div>
                    <div class="item-isUse" v-for="value in item.configInfoList" @click="checkTypeFun(item,value)">
                        <div class="success-icon"  v-if="value.isUse">
                            <div ><van-icon name="success" /></div>
                            已录</div>
                        <div class="error-icon" v-else><div ></div>未录</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 模拟网络接口柜 -->
    <div class="content" v-if="$route.query.type==2">
        <div class="content-monitor" v-for="item in sectionInfoList" :key="item.sectionId">
            <div class="content-monitor-title">
                {{ item.sectionName }}
            </div>
            <div class="content-monitor-item" >
                <div class="monior-item" v-for="item1 in item.configInfoList
" :key="item1.id">
                    <div class="title">{{ item1.configName }}</div>
                    <div :class="item1.isUse?'isusepass':'isusechange'" @click="checkTypeFun(item,item1)">
                        <van-icon name="success" v-if="item1.isUse" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 已录弹出层 -->
     <van-popup v-model="isusePopup" @close="closePopup" position="bottom" :style="{ height: '50%' }" >
         <div class="popup-container">
            <div class="popup-header">
                <h3 class="popup-title">{{ popuptitle }}</h3>
            </div>
            <div class="popup-sectionName">
                {{ unhireitem.configName }}
            </div>
            <div class="popup-sectionName" v-if="$route.query.type==2">
                {{ unhireitem.sectionName }}
            </div>
            <div class="popup-form">
                <div class="popup-form-item" style="display: flex;align-items: center;font-size: 14px;">
                    <!-- <van-field
                        v-model="unhireitem.number"
                        center
                        clearable
                        readonly
                        label-width="7rem"
                        :type="unhireitem.number&&unhireitem.number.length>=30?'textarea':''"
                        >
                        <template #label>
                           <div class="label-text">
                            序列号：
                           </div> 
                        </template>
                        <template #button>
                            <van-button size="small" type="info" @click="unbind">{{unhireitem.isUse?'解绑':'绑定'}}</van-button>
                        </template>
                        </van-field> -->
                        <!-- <div class="popup-form-item-title">
                            <div class="title">序列号：</div>
                            <div class="number">{{ unhireitem.number }}</div>
                            <div class="btn">
                                 <van-button size="small" type="info" @click="unbind">{{unhireitem.isUse?'解绑':'绑定'}}</van-button>
                            </div>
                        </div> -->
                           <div class="title" style="width: 8rem;font-size: 14px;color: #015a9d;">序列号：</div>
                            <div class="serial-number-display" style="flex: 1; padding: 0 10px;  white-space: wrap;">
                                {{ unhireitem.number }}
                            </div>
                            <van-button style="width: 65px;" size="small" type="info" @click="unbind">
                                {{unhireitem.isUse?'解绑':'绑定'}}
                            </van-button>
                </div>
                 <div class="popup-form-item">
                     <van-field
                        v-model="unhireitem.version"
                        center
                        clearable
                        label-width="7rem"
                        type="number"
                        :readonly="unhireitem.isUse"
                       
                        >
                          <!-- @click="versionPopup = true" -->
                        <template #label>
                           <div class="label-text">
                            版本号：
                           </div> 
                        </template>
                        </van-field>
                 </div>
            </div>
        </div>
     </van-popup>
     <!-- 版本弹出层 -->
    <van-popup v-model="versionPopup"  round
      :style="{ height: '60%' }" @close="closeversionPopup" :default-index="0" position="bottom" >
            <van-picker
            title="版本选择"  
            show-toolbar
            :columns="columns"
            @confirm="onConfirm"
            @cancel="onCancel"
            />
    </van-popup>

  </div>
</template>

<script>
import wx from "weixin-js-sdk";
import { mapState, mapMutations, mapGetters, mapActions } from "vuex";
export default {
  components: {},
  props: {},
  data() {
    return {
      trackData:[],
      titleList:[],
      sectionInfoList:[],
      selectedIndex: 0, // 记录当前选中的项
      selectedItem: {}, // 记录当前选中的项
      isusePopup:false,
      popuptitle:'',
      unhireitem:{},
      versionPopup:false,
    //   columns:['1.0','1.1','1.2'],
      columns:[
        {text:'1.0',value:'1.0'},
        {text:'1.1',value:'1.1'},
        {text:'1.2',value:'1.2'},
      ],
      isWeChat :false,

    };
  },
  computed: {
     ...mapGetters(["scanCodeInfo"]),
  },
  watch: {},
  //方法集合
  methods: {
    ...mapActions("repair_modules/repair", [
      "getConfigInfoOne",
       "categoryList",
       "numberVersionList",
       "bindOrUnbind"
    ]),
     ...mapMutations("repair_modules/repair", ["setScanCode"]),
    onClickLeft(){
      this.$router.go(-1)
    },
    selectItem(index,item) {
        console.log(item);
        // this.selectedItem=item
        this.popuptitle=item.trackCircuitConfig.drawingName
      // 如果点击的是已选中的项，则取消选择，否则选中该项
      this.selectedIndex = this.selectedIndex === index ? null : index;
      this.getdata(item.trackCircuitConfig.id)
    },
    async getcategoryList(){
        const res=await this.categoryList({
          stationId:this.$route.query.stationId
        })
        if(res.code==0){
            
          this.trackData = this.$route.query.type==1? res.data[0]:res.data[1]
          this.popuptitle=this.$route.query.type==1?
           res.data[0].children[0].trackCircuitConfig.drawingName:res.data[1].children[0].trackCircuitConfig.drawingName
          if(this.$route.query.type==1){
              this.getdata(this.trackData.children[0].trackCircuitConfig.id)
          }else{
            this.getdata(this.trackData.children[0].trackCircuitConfig.id)
          }
        
        }
    },
    async getdata(categoryId){
        const res=await this.getConfigInfoOne({
            configId:categoryId,
            stationId:this.$route.query.stationId,
            categoryId:this.$route.query.categoryId,
        })
        if(res.code==0){
            this.titleList=res.data.sectionInfoList[0].configInfoList
            this.sectionInfoList=res.data.sectionInfoList
            
        }
    },
     // 判断是否在微信环境
    isMiniProgram(callback) {
      var ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) != "micromessenger") {
        callback(false);
      } else {
        wx.miniProgram.getEnv((res) => {
          if (res.miniprogram) {
            callback(true); //小程序环境
          } else {
            callback(false);
          }
        });
      }
    },
    async checkTypeFun(item,value){
       
        if(value.isUse){
             this.unhire(value)
        }else{
             localStorage.setItem('sectionInfoItem',JSON.stringify(value))
             this.isMiniProgram((res)=>{
                if(res){
                     this.isWeChat = true;
                            this.$router.push({
            path:'/repair/scanCode',
            query:{
                type:'courierCode'
            }
           })
                }else{
                     this.isWeChat = false;
                     this.$router.push({
                    path:'trackCircuit/qrcode',
                })
                }
             })
       
             //    this.$router.push({
        //     path:'/repair/scanCode',
        //     query:{
        //         type:'courierCode'
        //     }
        //    })
        }
      

    },
    closePopup(){
        this.isusePopup=false
        this.popuptitle=''
        localStorage.removeItem('sectionInfoItem')
         localStorage.removeItem('csqrcode')
    },
    async unhire(value){
        console.log(value);
        
        this.isusePopup=true
          const res=await this.numberVersionList({
            sectionInfoId:value.id
        })
        if(res.code==0){
            this.unhireitem=res.data
        }else{
            this.$toast.fail(res.meg)
        }
    },
     async newunhire(value){
        console.log(value);
        
        this.isusePopup=true
          const res=await this.numberVersionList({
            sectionInfoId:JSON.parse(value).id
        })
        if(res.code==0){
            this.unhireitem=res.data
            if(this.isWeChat){
                 const scanCode = this.scanCodeInfo?.courierCode || "";
                 this.unhireitem.number=scanCode
            }else{
                const code= JSON.parse(localStorage.getItem('csqrcode'))

                this.unhireitem.number=code.decodedText
            }
           
        }else{
            this.$toast.fail(res.meg)
        }
    },
    async unbind(){
        
        this.$dialog.confirm({
            title: '提示',
            message: `是否要${this.unhireitem.isUse?'解绑':'绑定'}`,
            confirmButtonText: `确认${this.unhireitem.isUse?'解绑':'绑定'}`,
        }).then(async() => {
            let obj={
                id:this.unhireitem.id,
                isUse:this.unhireitem.isUse?false:true
            }
            if(!this.unhireitem.isUse){
                obj.number=this.unhireitem.number
                obj.version = this.unhireitem.version
            }
            const res=await this.bindOrUnbind(obj)
            if(res.code==0){
                this.setScanCode({})
                this.$toast.success(`${this.unhireitem.isUse?'解绑':'绑定'}成功！`)
                this.closePopup()
                 this.getcategoryList()
            }else{
                this.$toast.fail(res.msg||'操作失败')

            }
       })
    },
    onConfirm(value){
        this.unhireitem.version=value.text
        this.versionPopup=false
    },
    onCancel(){
        this.versionPopup=false
    },
    closeversionPopup(){
        this.versionPopup=false
    }

  },
  async created() {
    this.isMiniProgram((res) => {
      if (res) {
        console.log("isMiniProgram");
        this.isWeChat = true;
      } else {
        this.isWeChat = false;
      }
    });
  },
  mounted() { 
    console.log(localStorage.getItem('csqrcode'));

    
    if( localStorage.getItem('sectionInfoItem')){
        const item=localStorage.getItem('sectionInfoItem')
        if(this.scanCodeInfo.courierCode||localStorage.getItem('csqrcode')){
             this.newunhire(item)
        }
       
    }
    this.getcategoryList()
  },
};
</script>

<style lang="less" scoped>
.box{
  width: 100%;
  height: 100vh;
  background: #f0f8ff;
  // padding: 8px 10px 0px;
  box-sizing: border-box;
  overflow: auto;
//   display: flex;
//   flex-direction: column;
}
.header{
    width: 100%;
    padding: 10px 20px;
    background-color: #fff;
    display: flex;
    
}
.header-title {
  padding: 5px 10px;
  margin: 5px;
  background-color: #fff;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #000;

}

.header-title.active {
  background-color: #1989fa;
  color: #fff;
  border: none;
}
.content{
    width: 100%;
    padding: 10px;
    margin-top:10px ;
    background: #fff;
}
.content-box{
    width: 100%;
    display: flex;
}
.content-left{
    width: 15%;
    div{
        width: 100%;
        height: 60px;
        line-height: 60px;
        font-size: 16px;
        text-align: center;
        color: #015a9d;
        font-family: 黑体;
        font-weight: 700;
    }
}
.content-list{
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    .item{
        width: 19%;
        .item-title{
            color: #575757;
            height: 60px;
            line-height: 60px;
            font-size: 16px;
            font-family: 黑体;
            font-weight: 700;
        }
        .item-isUse{
            height: 60px;
            line-height: 60px;
            font-size: 16px;
            .success-icon{
                display: flex;
                align-items: center;
                color: #07c160;
                cursor: pointer;
                div{
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border: 1px dashed #07c160
                }
            }
            .error-icon{
                display: flex;
                align-items: center;
                color: #7c7575;
                  cursor: pointer;
                div{
                    width: 15px;
                    height: 15px;
                    border-radius: 50%;
                    
                    border: 1px dashed #a3a3a3
                }
            }
        }
    }
}
// Popup 弹出层样式
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.popup-header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #eee;

  .popup-title {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0;
    padding-bottom: 16px;
    text-align: center;
  }
  
}
.popup-sectionName{
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 20px 15px;
    font-weight: 600;
     color: #015a9d;;
  }
  .popup-form{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    .popup-form-item{
        width: 100%;
        padding: 10px;

        .popup-form-item-title{
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            .title{
                color: #015a9d;
            }
            .number{
                display: flex;
                flex-wrap: wrap;
                width: 60%;
            }
        }
    }
  }
.label-text{
    font-size: 14px;
     color: #015a9d;;
}
.content-monitor{ 
    width: 100%;
    .content-monitor-title{
        width: 100%;
        color: #333;
        font-size: 14px;
        font-weight: 600;
        padding: 10px;
    }
    .content-monitor-item{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-evenly;
        .monior-item{
            width: 22%;
            height: 60px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 10px 0;
            flex-direction: column;
            margin: 0 5px;
            .title{
                width: 100%;
                font-size: 14px;
                text-align: center;
            }
            .isusechange{
                font-size: 20px;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                
                border: 1px dashed #a3a3a3
            }
            .isusepass{
                font-size: 20px;
                width: 20px;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                color: #fff;
                background: #07c160;
                border: 1px dashed #07c160
            }
        }
    }
}
</style>