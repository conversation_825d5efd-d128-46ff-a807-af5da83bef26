{"remainingRequest": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js!E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue", "mtime": 1755670024153}, {"path": "E:\\smallProgram\\babel.config.js", "mtime": 1753929329699}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Html5Qrcode", "data", "html5QrCode", "created", "getCameras", "<PERSON><PERSON><PERSON><PERSON>", "stop", "methods", "_this", "then", "devices", "length", "start", "err", "$toast", "_this2", "facingMode", "fps", "qrbox", "width", "height", "decodedText", "decodedResult", "console", "log", "localStorage", "setItem", "JSON", "stringify", "$router", "go", "name", "ignore"], "sources": ["src/views/repairModule/trackCircuitqrcode.vue"], "sourcesContent": ["<template>\r\n  <div class=\"wrapper\">\r\n    <!-- <MyHeader :name=\"'调用摄像头扫码'\" left=\"arrow-left\" @goBackEv=\"$emit('goBack')\" /> -->\r\n    <div class=\"qrcode\">\r\n      <div id=\"reader\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Html5Qrcode } from \"html5-qrcode\";\r\nexport default {\r\n  //   components: { QrcodeStream },\r\n  data() {\r\n    return {\r\n      html5QrCode: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getCameras();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.html5QrCode) this.stop();\r\n  },\r\n  methods: {\r\n    getCameras() {\r\n      Html5Qrcode.getCameras()\r\n        .then((devices) => {\r\n          if (devices && devices.length) {\r\n            this.html5QrCode = new Html5Qrcode(\"reader\");\r\n            this.start();\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          // handle err\r\n          this.html5QrCode = new Html5Qrcode(\"reader\");\r\n          this.$toast(\"您需要授予相机访问权限\");\r\n        });\r\n    },\r\n    start() {\r\n      this.html5QrCode\r\n        .start(\r\n          // environment后置摄像头 user前置摄像头\r\n          { facingMode: \"environment\" },\r\n          {\r\n            fps: 2, // 可选，每秒帧扫描二维码\r\n            qrbox: { width: 250, height: 250 }, // 可选，如果你想要有界框UI\r\n            // aspectRatio: 1.777778 // 可选，视频馈送需要的纵横比，(4:3--1.333334, 16:9--1.777778, 1:1--1.0)传递错误的纵横比会导致视频不显示\r\n          },\r\n          (decodedText, decodedResult) => {\r\n            // do something when code is read\r\n            console.log(\"decodedText\", decodedText);\r\n            console.log(\"decodedResult\", decodedResult);\r\n            // this.$emit(\"goBack\", decodedText);\r\n            // this.$router.push(\"/DdingCoffee/blackList\");\r\n              localStorage.setItem('csqrcode',JSON.stringify(decodedResult))\r\n              this.$router.go(-1)\r\n          }\r\n        )\r\n        .catch((err) => {\r\n          console.log(\"扫码错误信息\", err);\r\n          // 错误信息处理仅供参考，具体情况看输出！！！\r\n          if (typeof err == \"string\") {\r\n            this.$toast(err);\r\n          } else {\r\n            if (err.name == \"NotAllowedError\")\r\n              return this.$toast(\"您需要授予相机访问权限\");\r\n            if (err.name == \"NotFoundError\")\r\n              return this.$toast(\"这个设备上没有摄像头\");\r\n            if (err.name == \"NotSupportedError\")\r\n              return this.$toast(\r\n                \"摄像头访问只支持在安全的上下文中，如https或localhost\"\r\n              );\r\n            if (err.name == \"NotReadableError\")\r\n              return this.$toast(\"相机被占用\");\r\n            if (err.name == \"OverconstrainedError\")\r\n              return this.$toast(\"安装摄像头不合适\");\r\n            if (err.name == \"StreamApiNotSupportedError\")\r\n              return this.$toast(\"此浏览器不支持流API\");\r\n          }\r\n        });\r\n    },\r\n    stop() {\r\n      this.html5QrCode\r\n        .stop()\r\n        .then((ignore) => {\r\n          // QR Code scanning is stopped.\r\n          console.log(\"QR Code scanning stopped.\");\r\n        })\r\n        .catch((err) => {\r\n          // Stop failed, handle it.\r\n          console.log(\"Unable to stop scanning.\");\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n \r\n<style lang=\"less\" scoped>\r\n.wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n  .qrcode {\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    #reader {\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translateY(-125px);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAUA,SAAAA,WAAA;AACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAH,WAAA,OAAAI,IAAA;EACA;EACAC,OAAA;IACAH,UAAA,WAAAA,WAAA;MAAA,IAAAI,KAAA;MACAR,WAAA,CAAAI,UAAA,GACAK,IAAA,WAAAC,OAAA;QACA,IAAAA,OAAA,IAAAA,OAAA,CAAAC,MAAA;UACAH,KAAA,CAAAN,WAAA,OAAAF,WAAA;UACAQ,KAAA,CAAAI,KAAA;QACA;MACA,WACA,WAAAC,GAAA;QACA;QACAL,KAAA,CAAAN,WAAA,OAAAF,WAAA;QACAQ,KAAA,CAAAM,MAAA;MACA;IACA;IACAF,KAAA,WAAAA,MAAA;MAAA,IAAAG,MAAA;MACA,KAAAb,WAAA,CACAU,KAAA;MACA;MACA;QAAAI,UAAA;MAAA,GACA;QACAC,GAAA;QAAA;QACAC,KAAA;UAAAC,KAAA;UAAAC,MAAA;QAAA;QACA;MACA,GACA,UAAAC,WAAA,EAAAC,aAAA;QACA;QACAC,OAAA,CAAAC,GAAA,gBAAAH,WAAA;QACAE,OAAA,CAAAC,GAAA,kBAAAF,aAAA;QACA;QACA;QACAG,YAAA,CAAAC,OAAA,aAAAC,IAAA,CAAAC,SAAA,CAAAN,aAAA;QACAP,MAAA,CAAAc,OAAA,CAAAC,EAAA;MACA,CACA,UACA,WAAAjB,GAAA;QACAU,OAAA,CAAAC,GAAA,WAAAX,GAAA;QACA;QACA,WAAAA,GAAA;UACAE,MAAA,CAAAD,MAAA,CAAAD,GAAA;QACA;UACA,IAAAA,GAAA,CAAAkB,IAAA,uBACA,OAAAhB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAkB,IAAA,qBACA,OAAAhB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAkB,IAAA,yBACA,OAAAhB,MAAA,CAAAD,MAAA,CACA,mCACA;UACA,IAAAD,GAAA,CAAAkB,IAAA,wBACA,OAAAhB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAkB,IAAA,4BACA,OAAAhB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAkB,IAAA,kCACA,OAAAhB,MAAA,CAAAD,MAAA;QACA;MACA;IACA;IACAR,IAAA,WAAAA,KAAA;MACA,KAAAJ,WAAA,CACAI,IAAA,GACAG,IAAA,WAAAuB,MAAA;QACA;QACAT,OAAA,CAAAC,GAAA;MACA,WACA,WAAAX,GAAA;QACA;QACAU,OAAA,CAAAC,GAAA;MACA;IACA;EACA;AACA"}]}