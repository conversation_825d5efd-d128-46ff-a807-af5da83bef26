{"remainingRequest": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js!E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue", "mtime": 1755674282493}, {"path": "E:\\smallProgram\\babel.config.js", "mtime": 1753929329699}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_html5Qrcode", "require", "data", "html5QrCode", "created", "getCameras", "<PERSON><PERSON><PERSON><PERSON>", "stop", "methods", "_this", "Html5Qrcode", "then", "devices", "length", "start", "err", "$toast", "_this2", "facingMode", "fps", "qrbox", "width", "height", "aspectRatio", "videoConstraints", "min", "ideal", "max", "focusMode", "zoom", "decodedText", "decodedResult", "console", "log", "localStorage", "setItem", "JSON", "stringify", "$router", "go", "name", "ignore"], "sources": ["src/views/repairModule/trackCircuitqrcode.vue"], "sourcesContent": ["<template>\r\n  <div class=\"wrapper\">\r\n    <!-- <MyHeader :name=\"'调用摄像头扫码'\" left=\"arrow-left\" @goBackEv=\"$emit('goBack')\" /> -->\r\n    <div class=\"qrcode\">\r\n      <div id=\"reader\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Html5Qrcode } from \"html5-qrcode\";\r\nexport default {\r\n  //   components: { QrcodeStream },\r\n  data() {\r\n    return {\r\n      html5QrCode: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getCameras();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.html5QrCode) this.stop();\r\n  },\r\n  methods: {\r\n    getCameras() {\r\n      Html5Qrcode.getCameras()\r\n        .then((devices) => {\r\n          if (devices && devices.length) {\r\n            this.html5QrCode = new Html5Qrcode(\"reader\");\r\n            this.start();\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          // handle err\r\n          this.html5QrCode = new Html5Qrcode(\"reader\");\r\n          this.$toast(\"您需要授予相机访问权限\");\r\n        });\r\n    },\r\n    start() {\r\n      this.html5QrCode\r\n        .start(\r\n          // environment后置摄像头 user前置摄像头\r\n          { facingMode: \"environment\" },\r\n          {\r\n            fps: 10, // 提高帧率以获得更好的扫描体验\r\n            qrbox: { width: 280, height: 280 }, // 启用扫描框UI，有助于对焦\r\n            aspectRatio: 1.0, // 1:1比例更适合二维码扫描\r\n            // 添加更多配置选项以优化小码扫描\r\n            videoConstraints: {\r\n              width: { min: 640, ideal: 1280, max: 1920 },\r\n              height: { min: 480, ideal: 720, max: 1080 },\r\n              // 启用自动对焦相关设置\r\n              focusMode: \"continuous\",\r\n              // 支持缩放以便扫描小码\r\n              zoom: true,\r\n              // 添加更多约束以获得更清晰的图像\r\n              facingMode: \"environment\"\r\n            },\r\n            // fps: 2, // 可选，每秒帧扫描二维码\r\n            // qrbox: { width: 250, height: 250 }, // 可选，如果你想要有界框UI\r\n            // aspectRatio: 1.777778 // 可选，视频馈送需要的纵横比，(4:3--1.333334, 16:9--1.777778, 1:1--1.0)传递错误的纵横比会导致视频不显示\r\n          },\r\n          (decodedText, decodedResult) => {\r\n            // do something when code is read\r\n            console.log(\"decodedText\", decodedText);\r\n            console.log(\"decodedResult\", decodedResult);\r\n            // this.$emit(\"goBack\", decodedText);\r\n            // this.$router.push(\"/DdingCoffee/blackList\");\r\n              localStorage.setItem('csqrcode',JSON.stringify(decodedResult))\r\n              this.$router.go(-1)\r\n          }\r\n        )\r\n        .catch((err) => {\r\n          console.log(\"扫码错误信息\", err);\r\n          // 错误信息处理仅供参考，具体情况看输出！！！\r\n          if (typeof err == \"string\") {\r\n            this.$toast(err);\r\n          } else {\r\n            if (err.name == \"NotAllowedError\")\r\n              return this.$toast(\"您需要授予相机访问权限\");\r\n            if (err.name == \"NotFoundError\")\r\n              return this.$toast(\"这个设备上没有摄像头\");\r\n            if (err.name == \"NotSupportedError\")\r\n              return this.$toast(\r\n                \"摄像头访问只支持在安全的上下文中，如https或localhost\"\r\n              );\r\n            if (err.name == \"NotReadableError\")\r\n              return this.$toast(\"相机被占用\");\r\n            if (err.name == \"OverconstrainedError\")\r\n              return this.$toast(\"安装摄像头不合适\");\r\n            if (err.name == \"StreamApiNotSupportedError\")\r\n              return this.$toast(\"此浏览器不支持流API\");\r\n          }\r\n        });\r\n    },\r\n    stop() {\r\n      this.html5QrCode\r\n        .stop()\r\n        .then((ignore) => {\r\n          // QR Code scanning is stopped.\r\n          console.log(\"QR Code scanning stopped.\");\r\n        })\r\n        .catch((err) => {\r\n          // Stop failed, handle it.\r\n          console.log(\"Unable to stop scanning.\");\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n \r\n<style lang=\"less\" scoped>\r\n.wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n  .qrcode {\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    #reader {\r\n      top: 20%;\r\n      left: 0;\r\n      transform: translateY(-125px);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAUA,IAAAA,YAAA,GAAAC,OAAA;;;;;;;;;;oCACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAH,WAAA,OAAAI,IAAA;EACA;EACAC,OAAA;IACAH,UAAA,WAAAA,WAAA;MAAA,IAAAI,KAAA;MACAC,wBAAA,CAAAL,UAAA,GACAM,IAAA,WAAAC,OAAA;QACA,IAAAA,OAAA,IAAAA,OAAA,CAAAC,MAAA;UACAJ,KAAA,CAAAN,WAAA,OAAAO,wBAAA;UACAD,KAAA,CAAAK,KAAA;QACA;MACA,WACA,WAAAC,GAAA;QACA;QACAN,KAAA,CAAAN,WAAA,OAAAO,wBAAA;QACAD,KAAA,CAAAO,MAAA;MACA;IACA;IACAF,KAAA,WAAAA,MAAA;MAAA,IAAAG,MAAA;MACA,KAAAd,WAAA,CACAW,KAAA;MACA;MACA;QAAAI,UAAA;MAAA,GACA;QACAC,GAAA;QAAA;QACAC,KAAA;UAAAC,KAAA;UAAAC,MAAA;QAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAC,gBAAA;UACAH,KAAA;YAAAI,GAAA;YAAAC,KAAA;YAAAC,GAAA;UAAA;UACAL,MAAA;YAAAG,GAAA;YAAAC,KAAA;YAAAC,GAAA;UAAA;UACA;UACAC,SAAA;UACA;UACAC,IAAA;UACA;UACAX,UAAA;QACA;QACA;QACA;QACA;MACA,GACA,UAAAY,WAAA,EAAAC,aAAA;QACA;QACAC,OAAA,CAAAC,GAAA,gBAAAH,WAAA;QACAE,OAAA,CAAAC,GAAA,kBAAAF,aAAA;QACA;QACA;QACAG,YAAA,CAAAC,OAAA,aAAAC,IAAA,CAAAC,SAAA,CAAAN,aAAA;QACAd,MAAA,CAAAqB,OAAA,CAAAC,EAAA;MACA,CACA,UACA,WAAAxB,GAAA;QACAiB,OAAA,CAAAC,GAAA,WAAAlB,GAAA;QACA;QACA,WAAAA,GAAA;UACAE,MAAA,CAAAD,MAAA,CAAAD,GAAA;QACA;UACA,IAAAA,GAAA,CAAAyB,IAAA,uBACA,OAAAvB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAyB,IAAA,qBACA,OAAAvB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAyB,IAAA,yBACA,OAAAvB,MAAA,CAAAD,MAAA,CACA,mCACA;UACA,IAAAD,GAAA,CAAAyB,IAAA,wBACA,OAAAvB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAyB,IAAA,4BACA,OAAAvB,MAAA,CAAAD,MAAA;UACA,IAAAD,GAAA,CAAAyB,IAAA,kCACA,OAAAvB,MAAA,CAAAD,MAAA;QACA;MACA;IACA;IACAT,IAAA,WAAAA,KAAA;MACA,KAAAJ,WAAA,CACAI,IAAA,GACAI,IAAA,WAAA8B,MAAA;QACA;QACAT,OAAA,CAAAC,GAAA;MACA,WACA,WAAAlB,GAAA;QACA;QACAiB,OAAA,CAAAC,GAAA;MACA;IACA;EACA;AACA"}]}