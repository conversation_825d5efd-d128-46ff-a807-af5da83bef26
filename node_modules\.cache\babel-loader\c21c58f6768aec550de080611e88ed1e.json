{"remainingRequest": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js!E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js!E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\sign\\blankSign.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\sign\\blankSign.vue", "mtime": 1755504836657}, {"path": "E:\\smallProgram\\babel.config.js", "mtime": 1753929329699}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751509197733}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}