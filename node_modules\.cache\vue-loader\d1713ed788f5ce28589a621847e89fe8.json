{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\singleProduct\\mobileView.vue?vue&type=style&index=0&id=6ca19eee&scoped=true&lang=less", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\singleProduct\\mobileView.vue", "mtime": 1751451780502}, {"path": "E:\\smallProgram\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751509198155}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751509199665}, {"path": "E:\\smallProgram\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751509198719}, {"path": "E:\\smallProgram\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1751509198495}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoNCi5kcmx4bSB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGhlaWdodDogMTAwdmg7DQogIHBhZGRpbmc6IDE0dmggMjVweCAwIDI1cHg7DQp9DQouYm94Qm9yZGVyIHsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYm9yZGVyOiAxcHggc29saWQgI2NjYzsNCiAgcGFkZGluZzogMTVweCAwOw0KICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KfQ0KaW1nIHsNCiAgd2lkdGg6IDYwcHg7DQogIGhlaWdodDogNjBweDsNCn0NCi50aXRsZTEgew0KICBtYXJnaW46IDE1cHggMCAxMHB4IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM4Nzg3ODc7DQp9DQo="}, {"version": 3, "sources": ["mobileView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "mobileView.vue", "sourceRoot": "src/views/singleProduct", "sourcesContent": ["<template>\r\n  <div class=\"drlxm\">\r\n    <div class=\"flex_center boxBorder\">\r\n      <img src=\"@/assets/icon.png\" alt=\"\">\r\n      <div class=\"title1\">请到手机端钉钉待办中处理</div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: \"mobileViewH5\",\r\n  data () {\r\n    return {\r\n    }\r\n  },\r\n};\r\n</script>\r\n <style scoped lang=\"less\">\r\n.drlxm {\r\n  background: #fff;\r\n  height: 100vh;\r\n  padding: 14vh 25px 0 25px;\r\n}\r\n.boxBorder {\r\n  flex-direction: column;\r\n  border: 1px solid #ccc;\r\n  padding: 15px 0;\r\n  border-radius: 10px;\r\n}\r\nimg {\r\n  width: 60px;\r\n  height: 60px;\r\n}\r\n.title1 {\r\n  margin: 15px 0 10px 0;\r\n  font-size: 14px;\r\n  color: #878787;\r\n}\r\n</style>\r\n\r\n"]}]}