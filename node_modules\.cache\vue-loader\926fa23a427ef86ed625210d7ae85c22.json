{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue", "mtime": 1755674718262}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["trackCircuitqrcode.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "trackCircuitqrcode.vue", "sourceRoot": "src/views/repairModule", "sourcesContent": ["<template>\r\n  <div class=\"wrapper\">\r\n    <!-- <MyHeader :name=\"'调用摄像头扫码'\" left=\"arrow-left\" @goBackEv=\"$emit('goBack')\" /> -->\r\n    <div class=\"qrcode\">\r\n      <div id=\"reader\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Html5Qrcode } from \"html5-qrcode\";\r\nexport default {\r\n  //   components: { QrcodeStream },\r\n  data() {\r\n    return {\r\n      html5QrCode: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getCameras();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.html5QrCode) this.stop();\r\n  },\r\n  methods: {\r\n    getCameras() {\r\n      Html5Qrcode.getCameras()\r\n        .then((devices) => {\r\n          if (devices && devices.length) {\r\n            this.html5QrCode = new Html5Qrcode(\"reader\");\r\n            this.start();\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          // handle err\r\n          this.html5QrCode = new Html5Qrcode(\"reader\");\r\n          this.$toast(\"您需要授予相机访问权限\");\r\n        });\r\n    },\r\n    start() {\r\n      this.html5QrCode\r\n        .start(\r\n          // 摄像头约束配置，包含缩放和对焦设置\r\n          {\r\n            facingMode: \"environment\",\r\n            // 添加高分辨率和缩放支持\r\n            width: { min: 640, ideal: 1280, max: 1920 },\r\n            height: { min: 480, ideal: 720, max: 1080 },\r\n            // 启用自动对焦\r\n            focusMode: \"continuous\",\r\n            // 启用缩放功能 - 这是关键配置\r\n            zoom: { min: 1.0, max: 3.0 }\r\n          },\r\n          {\r\n            fps: 10, // 提高帧率以获得更好的扫描体验\r\n            qrbox: { width: 280, height: 280 }, // 启用扫描框UI，有助于对焦\r\n            aspectRatio: 1.0 // 1:1比例更适合二维码扫描\r\n          },\r\n          (decodedText, decodedResult) => {\r\n            // do something when code is read\r\n            console.log(\"decodedText\", decodedText);\r\n            console.log(\"decodedResult\", decodedResult);\r\n\r\n\r\n            localStorage.setItem('csqrcode', JSON.stringify(decodedResult));\r\n            this.$router.go(-1);\r\n          }\r\n        )\r\n        .catch((err) => {\r\n          console.log(\"扫码错误信息\", err);\r\n          // 错误信息处理仅供参考，具体情况看输出！！！\r\n          if (typeof err == \"string\") {\r\n            this.$toast(err);\r\n          } else {\r\n            if (err.name == \"NotAllowedError\")\r\n              return this.$toast(\"您需要授予相机访问权限\");\r\n            if (err.name == \"NotFoundError\")\r\n              return this.$toast(\"这个设备上没有摄像头\");\r\n            if (err.name == \"NotSupportedError\")\r\n              return this.$toast(\r\n                \"摄像头访问只支持在安全的上下文中，如https或localhost\"\r\n              );\r\n            if (err.name == \"NotReadableError\")\r\n              return this.$toast(\"相机被占用\");\r\n            if (err.name == \"OverconstrainedError\")\r\n              return this.$toast(\"摄像头配置不支持，已尝试基本配置\");\r\n            if (err.name == \"StreamApiNotSupportedError\")\r\n              return this.$toast(\"此浏览器不支持流API\");\r\n          }\r\n        });\r\n    },\r\n\r\n    stop() {\r\n      this.html5QrCode\r\n        .stop()\r\n        .then((ignore) => {\r\n          // QR Code scanning is stopped.\r\n          console.log(\"QR Code scanning stopped.\");\r\n        })\r\n        .catch((err) => {\r\n          // Stop failed, handle it.\r\n          console.log(\"Unable to stop scanning.\");\r\n        });\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n \r\n<style lang=\"less\" scoped>\r\n.wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n  .qrcode {\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    #reader {\r\n      top: 20%;\r\n      left: 0;\r\n      transform: translateY(-125px);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}