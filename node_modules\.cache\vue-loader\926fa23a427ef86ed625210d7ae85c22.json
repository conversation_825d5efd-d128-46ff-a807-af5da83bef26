{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\repairModule\\trackCircuitqrcode.vue", "mtime": 1755673057765}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["trackCircuitqrcode.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "trackCircuitqrcode.vue", "sourceRoot": "src/views/repairModule", "sourcesContent": ["<template>\r\n  <div class=\"wrapper\">\r\n    <!-- <MyHeader :name=\"'调用摄像头扫码'\" left=\"arrow-left\" @goBackEv=\"$emit('goBack')\" /> -->\r\n    <div class=\"qrcode\">\r\n      <div id=\"reader\"></div>\r\n      <!-- 添加对焦提示和手动对焦按钮 -->\r\n      <div class=\"focus-controls\">\r\n        <div class=\"focus-tip\">对准二维码，轻触屏幕对焦</div>\r\n        <button class=\"focus-btn\" @click=\"requestFocus\" v-if=\"isScanning\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"3\"/>\r\n            <path d=\"M12 1v6m0 6v6m11-7h-6m-6 0H1\"/>\r\n          </svg>\r\n          对焦\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Html5Qrcode, Html5QrcodeScanType } from \"html5-qrcode\";\r\nexport default {\r\n  //   components: { QrcodeStream },\r\n  data() {\r\n    return {\r\n      html5QrCode: null,\r\n      isScanning: false,\r\n      cameraId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getCameras();\r\n  },\r\n  beforeDestroy() {\r\n    if (this.html5QrCode) this.stop();\r\n  },\r\n  methods: {\r\n    getCameras() {\r\n      Html5Qrcode.getCameras()\r\n        .then((devices) => {\r\n          if (devices && devices.length) {\r\n            // 优先选择后置摄像头，通常具有更好的自动对焦功能\r\n            const backCamera = devices.find(device =>\r\n              device.label.toLowerCase().includes('back') ||\r\n              device.label.toLowerCase().includes('rear') ||\r\n              device.label.toLowerCase().includes('environment')\r\n            );\r\n            this.cameraId = backCamera ? backCamera.id : devices[devices.length - 1].id;\r\n            this.html5QrCode = new Html5Qrcode(\"reader\");\r\n            this.start();\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          /* eslint-disable */console.error(...oo_tx(`3747191824_55_10_55_40_11`,\"获取摄像头失败:\", err));\r\n          this.html5QrCode = new Html5Qrcode(\"reader\");\r\n          this.$toast(\"您需要授予相机访问权限\");\r\n        });\r\n    },\r\n    start() {\r\n      if (this.isScanning) return;\r\n\r\n      // 使用选定的摄像头ID或回退到facingMode\r\n      const cameraConfig = this.cameraId ? this.cameraId : { facingMode: \"environment\" };\r\n\r\n      this.html5QrCode\r\n        .start(\r\n          cameraConfig,\r\n          {\r\n            fps: 10, // 提高帧率以获得更好的扫描体验\r\n            qrbox: { width: 280, height: 280 }, // 启用扫描框UI，有助于对焦\r\n            aspectRatio: 1.0, // 1:1比例更适合二维码扫描\r\n            // 添加更多配置选项以优化小码扫描\r\n            videoConstraints: {\r\n              width: { min: 640, ideal: 1280, max: 1920 },\r\n              height: { min: 480, ideal: 720, max: 1080 },\r\n              // 启用自动对焦相关设置\r\n              focusMode: \"continuous\",\r\n              // 支持缩放以便扫描小码\r\n              zoom: true,\r\n              // 添加更多约束以获得更清晰的图像\r\n              facingMode: \"environment\"\r\n            },\r\n            // 支持多种码类型，包括小的条形码\r\n            supportedScanTypes: [\r\n              Html5QrcodeScanType.SCAN_TYPE_CAMERA\r\n            ]\r\n          },\r\n          (decodedText, decodedResult) => {\r\n            // do something when code is read\r\n            /* eslint-disable */console.log(...oo_oo(`3747191824_91_12_91_51_4`,\"decodedText\", decodedText));\r\n            /* eslint-disable */console.log(...oo_oo(`3747191824_92_12_92_55_4`,\"decodedResult\", decodedResult));\r\n\r\n            // 扫描成功后立即停止扫描\r\n            this.isScanning = false;\r\n            this.stop();\r\n\r\n            // 保存结果并返回\r\n            localStorage.setItem('csqrcode', JSON.stringify(decodedResult));\r\n            this.$router.go(-1);\r\n          }\r\n        )\r\n        .then(() => {\r\n          this.isScanning = true;\r\n          /* eslint-disable */console.log(...oo_oo(`3747191824_105_10_105_63_4`,\"QR Code scanning started successfully.\"));\r\n        })\r\n        .catch((err) => {\r\n          this.isScanning = false;\r\n          /* eslint-disable */console.log(...oo_oo(`3747191824_109_10_109_36_4`,\"扫码错误信息\", err));\r\n\r\n          // 如果是约束错误，尝试使用更基本的配置重新启动\r\n          if (err.name === \"OverconstrainedError\") {\r\n            /* eslint-disable */console.log(...oo_oo(`3747191824_113_12_113_42_4`,\"尝试使用基本配置重新启动...\"));\r\n            this.startWithBasicConfig();\r\n            return;\r\n          }\r\n\r\n          // 错误信息处理\r\n          if (typeof err == \"string\") {\r\n            this.$toast(err);\r\n          } else {\r\n            if (err.name == \"NotAllowedError\")\r\n              return this.$toast(\"您需要授予相机访问权限\");\r\n            if (err.name == \"NotFoundError\")\r\n              return this.$toast(\"这个设备上没有摄像头\");\r\n            if (err.name == \"NotSupportedError\")\r\n              return this.$toast(\r\n                \"摄像头访问只支持在安全的上下文中，如https或localhost\"\r\n              );\r\n            if (err.name == \"NotReadableError\")\r\n              return this.$toast(\"相机被占用\");\r\n            if (err.name == \"OverconstrainedError\")\r\n              return this.$toast(\"摄像头配置不支持，已尝试基本配置\");\r\n            if (err.name == \"StreamApiNotSupportedError\")\r\n              return this.$toast(\"此浏览器不支持流API\");\r\n          }\r\n        });\r\n    },\r\n    // 基本配置的备用启动方法\r\n    startWithBasicConfig() {\r\n      if (this.isScanning) return;\r\n\r\n      const cameraConfig = this.cameraId ? this.cameraId : { facingMode: \"environment\" };\r\n\r\n      this.html5QrCode\r\n        .start(\r\n          cameraConfig,\r\n          {\r\n            fps: 10,\r\n            qrbox: { width: 250, height: 250 },\r\n            aspectRatio: 1.0\r\n          },\r\n          (decodedText, decodedResult) => {\r\n            this.isScanning = false;\r\n            this.stop();\r\n            localStorage.setItem('csqrcode', JSON.stringify(decodedResult));\r\n            this.$router.go(-1);\r\n          }\r\n        )\r\n        .then(() => {\r\n          this.isScanning = true;\r\n          /* eslint-disable */console.log(...oo_oo(`3747191824_162_10_162_68_4`,\"QR Code scanning started with basic config.\"));\r\n        })\r\n        .catch((err) => {\r\n          this.isScanning = false;\r\n          /* eslint-disable */console.error(...oo_tx(`3747191824_166_10_166_42_11`,\"基本配置启动也失败:\", err));\r\n          this.$toast(\"摄像头启动失败，请检查设备权限\");\r\n        });\r\n    },\r\n\r\n    stop() {\r\n      if (!this.html5QrCode || !this.isScanning) return;\r\n\r\n      this.html5QrCode\r\n        .stop()\r\n        .then(() => {\r\n          this.isScanning = false;\r\n          /* eslint-disable */console.log(...oo_oo(`3747191824_178_10_178_50_4`,\"QR Code scanning stopped.\"));\r\n        })\r\n        .catch((err) => {\r\n          /* eslint-disable */console.error(...oo_tx(`3747191824_181_10_181_39_11`,\"停止扫描失败:\", err));\r\n          this.isScanning = false;\r\n        });\r\n    },\r\n\r\n    // 手动对焦方法\r\n    async requestFocus() {\r\n      try {\r\n        // 获取视频流\r\n        const videoElement = document.querySelector('#reader video');\r\n        if (videoElement && videoElement.srcObject) {\r\n          const stream = videoElement.srcObject;\r\n          const videoTrack = stream.getVideoTracks()[0];\r\n\r\n          // 检查是否支持对焦功能\r\n          const capabilities = videoTrack.getCapabilities();\r\n          if (capabilities.focusMode && capabilities.focusMode.includes('manual')) {\r\n            // 尝试手动对焦\r\n            await videoTrack.applyConstraints({\r\n              advanced: [{ focusMode: 'manual', focusDistance: 0.1 }]\r\n            });\r\n\r\n            // 短暂延迟后切换回连续对焦\r\n            setTimeout(async () => {\r\n              try {\r\n                await videoTrack.applyConstraints({\r\n                  advanced: [{ focusMode: 'continuous' }]\r\n                });\r\n              } catch (e) {\r\n                /* eslint-disable */console.log(...oo_oo(`3747191824_210_16_210_44_4`,'切换回连续对焦失败:', e));\r\n              }\r\n            }, 1000);\r\n\r\n            this.$toast('正在对焦...');\r\n          } else {\r\n            this.$toast('设备不支持手动对焦');\r\n          }\r\n        }\r\n      } catch (error) {\r\n        /* eslint-disable */console.error(...oo_tx(`3747191824_220_8_220_37_11`,'对焦失败:', error));\r\n        this.$toast('对焦失败，请重新扫描');\r\n      }\r\n    },\r\n  },\r\n};\r\n/* istanbul ignore next *//* c8 ignore start *//* eslint-disable */;function oo_cm(){try{return (0,eval)(\"globalThis._console_ninja\") || (0,eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x306708=_0x57eb;(function(_0x2bb52b,_0xc34b72){var _0x530621=_0x57eb,_0xc55af5=_0x2bb52b();while(!![]){try{var _0x24c23f=parseInt(_0x530621(0x201))/0x1*(-parseInt(_0x530621(0x25f))/0x2)+parseInt(_0x530621(0x209))/0x3*(-parseInt(_0x530621(0x196))/0x4)+-parseInt(_0x530621(0x21e))/0x5+parseInt(_0x530621(0x25b))/0x6+parseInt(_0x530621(0x1d7))/0x7*(-parseInt(_0x530621(0x1d3))/0x8)+parseInt(_0x530621(0x202))/0x9*(-parseInt(_0x530621(0x1f0))/0xa)+parseInt(_0x530621(0x20c))/0xb;if(_0x24c23f===_0xc34b72)break;else _0xc55af5['push'](_0xc55af5['shift']());}catch(_0x3c622c){_0xc55af5['push'](_0xc55af5['shift']());}}}(_0x4ac4,0x6fa73));function _0x4ac4(){var _0x43cb65=['resetOnProcessingTimeAverageMs','cappedProps','autoExpandPropertyCount','...','date','https://tinyurl.com/37x8b79t','origin','autoExpandLimit','autoExpand','host','coverage','_getOwnPropertySymbols','1','sortProps','capped','_p_','then','_additionalMetadata','_setNodeExpandableState','Set','unknown','number','defineProperty','\\\\x20server','isExpressionToEvaluate','_p_name','length','test','autoExpandMaxDepth','HTMLAllCollection','console','<LOG_LIMITS>','_sendErrorMessage','_console_ninja','global','hostname','_reconnectTimeout','_treeNodePropertiesBeforeFullValue','String','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','unref','_inNextEdge','send','totalStrLength','nodeModules','getPrototypeOf','elapsed','autoExpandPreviousObjects','_connectAttemptCount','reducedLimits','setter','endsWith','_setNodeLabel','cappedElements','onclose','8wFqTbY','_addFunctionsNode','_numberRegExp','count','144781QBgSlB','NEXT_RUNTIME','null','_ninjaIgnoreNextError','create','symbol','reload','strLength','nan','expressionsToEvaluate','map','_hasSetOnItsPath','string','remix','_allowedToConnectOnSend','rootExpression','_capIfString','get','reduceOnAccumulatedProcessingTimeMs','_disposeWebsocket','_keyStrRegExp','Map','toLowerCase','array','webpack','838570VmfIKn','reduceLimits','getOwnPropertyDescriptor','_treeNodePropertiesAfterFullValue','match','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)','_isUndefined','_maxConnectAttemptCount','resetWhenQuietMs','_Symbol','function','now','negativeInfinity','_sortProps','depth','env','allStrLength','1JzERwx','63wQTjHb','message','fromCharCode','_getOwnPropertyNames','object','resolveGetters','readyState','6EUSimZ','undefined','_addProperty','25659007VFMgxT','_propertyName','_type','_isPrimitiveType','url','index','_isArray','angular','_connected','_objectToString','onopen','stack','_quotedRegExp','dockerizedApp','call','pop','disabledLog','type','2784920reJBoY','_allowedToSend','replace','_extendedWarning','_inBrowser','defaultLimits','indexOf','getWebSocketClass','warn','_attemptToReconnectShortly','NEGATIVE_INFINITY','name','_setNodeExpressionPath','join','disabledTrace','forEach','timeStamp','astro','bigint','[object\\\\x20Date]','Number','level','_addObjectProperty','getter','POSITIVE_INFINITY','reduceOnCount','toUpperCase','positiveInfinity','[object\\\\x20Map]','args','_property','isArray','_setNodeQueryPath','default','catch','includes','Error','_addLoadNode','RegExp','serialize','constructor','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket','_isMap','root_exp_id','performance','_HTMLAllCollection','_console_ninja_session','__es'+'Module','current','Boolean','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','toString','_hasSymbolPropertyOnItsPath','_WebSocketClass','trace','concat','hits','_socket','reducePolicy','next.js','5053704ydudrA','[object\\\\x20BigInt]','WebSocket','process','1742492lNZmIA','_setNodePermissions','Buffer','enumerable','versions','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','_regExpToString','_WebSocket','_blacklistedProperty','_undefined','prototype','time','method','_ws','_isPrimitiveWrapperType','push','perf_hooks','_cleanNode','[object\\\\x20Set]','boolean','getOwnPropertyNames','127.0.0.1','slice','elements','port','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','logger\\\\x20websocket\\\\x20error','_consoleNinjaAllowedToStart','path','_p_length','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','noFunctions','expId',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"ISS\\\",\\\"*************\\\"],'negativeZero','_getOwnPropertyDescriptor','_dateToString','_processTreeNodeResult','split','location','_connectToHostNow','ws/index.js','stringify','_setNodeId','eventReceivedCallback','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','value','edge','log','gateway.docker.internal','stackTraceLimit','_connecting','hasOwnProperty','_hasMapOnItsPath','root_exp','perLogpoint','Symbol','props','hrtime','parse','parent','substr','_isSet','error','1363356HEYciT','onerror','node','close','_webSocketErrorDocsLink','_isNegativeZero'];_0x4ac4=function(){return _0x43cb65;};return _0x4ac4();}var te=Object[_0x306708(0x1db)],G=Object[_0x306708(0x1b2)],ne=Object[_0x306708(0x1f2)],re=Object[_0x306708(0x16a)],ie=Object[_0x306708(0x1c9)],se=Object[_0x306708(0x269)][_0x306708(0x18a)],oe=(_0x4e7daa,_0x3f22a0,_0x127e05,_0x45c74e)=>{var _0xf288d1=_0x306708;if(_0x3f22a0&&typeof _0x3f22a0==_0xf288d1(0x206)||typeof _0x3f22a0==_0xf288d1(0x1fa)){for(let _0x1b70c5 of re(_0x3f22a0))!se[_0xf288d1(0x21a)](_0x4e7daa,_0x1b70c5)&&_0x1b70c5!==_0x127e05&&G(_0x4e7daa,_0x1b70c5,{'get':()=>_0x3f22a0[_0x1b70c5],'enumerable':!(_0x45c74e=ne(_0x3f22a0,_0x1b70c5))||_0x45c74e[_0xf288d1(0x262)]});}return _0x4e7daa;},K=(_0x87d7de,_0x1c5524,_0x373b7b)=>(_0x373b7b=_0x87d7de!=null?te(ie(_0x87d7de)):{},oe(_0x1c5524||!_0x87d7de||!_0x87d7de[_0x306708(0x24e)]?G(_0x373b7b,_0x306708(0x23f),{'value':_0x87d7de,'enumerable':!0x0}):_0x373b7b,_0x87d7de)),H=class{constructor(_0x5cb8c2,_0x5cddf3,_0x502c73,_0x27288c,_0x49d611,_0x8dd291){var _0x481b37=_0x306708,_0x3d6120,_0x5d2881,_0x91ba19,_0x3fd311;this['global']=_0x5cb8c2,this['host']=_0x5cddf3,this['port']=_0x502c73,this['nodeModules']=_0x27288c,this['dockerizedApp']=_0x49d611,this[_0x481b37(0x182)]=_0x8dd291,this['_allowedToSend']=!0x0,this[_0x481b37(0x1e5)]=!0x0,this[_0x481b37(0x214)]=!0x1,this[_0x481b37(0x189)]=!0x1,this[_0x481b37(0x1c5)]=((_0x5d2881=(_0x3d6120=_0x5cb8c2['process'])==null?void 0x0:_0x3d6120['env'])==null?void 0x0:_0x5d2881[_0x481b37(0x1d8)])===_0x481b37(0x185),this[_0x481b37(0x222)]=!((_0x3fd311=(_0x91ba19=this[_0x481b37(0x1be)]['process'])==null?void 0x0:_0x91ba19[_0x481b37(0x263)])!=null&&_0x3fd311[_0x481b37(0x198)])&&!this[_0x481b37(0x1c5)],this[_0x481b37(0x254)]=null,this['_connectAttemptCount']=0x0,this['_maxConnectAttemptCount']=0x14,this[_0x481b37(0x19a)]=_0x481b37(0x1a1),this['_sendErrorMessage']=(this[_0x481b37(0x222)]?_0x481b37(0x183):_0x481b37(0x1c3))+this[_0x481b37(0x19a)];}async['getWebSocketClass'](){var _0xf6c3da=_0x306708,_0x4c74a4,_0x42b90b;if(this[_0xf6c3da(0x254)])return this[_0xf6c3da(0x254)];let _0x1ae56c;if(this[_0xf6c3da(0x222)]||this[_0xf6c3da(0x1c5)])_0x1ae56c=this[_0xf6c3da(0x1be)][_0xf6c3da(0x25d)];else{if((_0x4c74a4=this[_0xf6c3da(0x1be)][_0xf6c3da(0x25e)])!=null&&_0x4c74a4[_0xf6c3da(0x266)])_0x1ae56c=(_0x42b90b=this[_0xf6c3da(0x1be)]['process'])==null?void 0x0:_0x42b90b[_0xf6c3da(0x266)];else try{let _0x1a51de=await import(_0xf6c3da(0x172));_0x1ae56c=(await import((await import(_0xf6c3da(0x210)))['pathToFileURL'](_0x1a51de[_0xf6c3da(0x22b)](this['nodeModules'],_0xf6c3da(0x17f)))[_0xf6c3da(0x252)]()))[_0xf6c3da(0x23f)];}catch{try{_0x1ae56c=require(require(_0xf6c3da(0x172))[_0xf6c3da(0x22b)](this[_0xf6c3da(0x1c8)],'ws'));}catch{throw new Error(_0xf6c3da(0x248));}}}return this['_WebSocketClass']=_0x1ae56c,_0x1ae56c;}['_connectToHostNow'](){var _0x14b20e=_0x306708;this[_0x14b20e(0x189)]||this['_connected']||this[_0x14b20e(0x1cc)]>=this[_0x14b20e(0x1f7)]||(this[_0x14b20e(0x1e5)]=!0x1,this[_0x14b20e(0x189)]=!0x0,this[_0x14b20e(0x1cc)]++,this[_0x14b20e(0x26c)]=new Promise((_0x504528,_0x272872)=>{var _0x53e4bd=_0x14b20e;this[_0x53e4bd(0x225)]()['then'](_0x58f37c=>{var _0x4fc413=_0x53e4bd;let _0x3b1416=new _0x58f37c('ws://'+(!this[_0x4fc413(0x222)]&&this[_0x4fc413(0x219)]?_0x4fc413(0x187):this[_0x4fc413(0x1a5)])+':'+this[_0x4fc413(0x16e)]);_0x3b1416['onerror']=()=>{var _0x4950fe=_0x4fc413;this[_0x4950fe(0x21f)]=!0x1,this[_0x4950fe(0x1ea)](_0x3b1416),this[_0x4950fe(0x227)](),_0x272872(new Error(_0x4950fe(0x170)));},_0x3b1416[_0x4fc413(0x216)]=()=>{var _0x1a2bb5=_0x4fc413;this[_0x1a2bb5(0x222)]||_0x3b1416[_0x1a2bb5(0x258)]&&_0x3b1416[_0x1a2bb5(0x258)][_0x1a2bb5(0x1c4)]&&_0x3b1416[_0x1a2bb5(0x258)][_0x1a2bb5(0x1c4)](),_0x504528(_0x3b1416);},_0x3b1416[_0x4fc413(0x1d2)]=()=>{var _0x4e6dc4=_0x4fc413;this[_0x4e6dc4(0x1e5)]=!0x0,this['_disposeWebsocket'](_0x3b1416),this[_0x4e6dc4(0x227)]();},_0x3b1416['onmessage']=_0xe37bc=>{var _0x2b587b=_0x4fc413;try{if(!(_0xe37bc!=null&&_0xe37bc['data'])||!this[_0x2b587b(0x182)])return;let _0x118da=JSON[_0x2b587b(0x191)](_0xe37bc['data']);this[_0x2b587b(0x182)](_0x118da[_0x2b587b(0x26b)],_0x118da[_0x2b587b(0x23b)],this[_0x2b587b(0x1be)],this[_0x2b587b(0x222)]);}catch{}};})[_0x53e4bd(0x1ac)](_0x321927=>(this[_0x53e4bd(0x214)]=!0x0,this['_connecting']=!0x1,this[_0x53e4bd(0x1e5)]=!0x1,this['_allowedToSend']=!0x0,this['_connectAttemptCount']=0x0,_0x321927))[_0x53e4bd(0x240)](_0x369a11=>(this[_0x53e4bd(0x214)]=!0x1,this[_0x53e4bd(0x189)]=!0x1,console[_0x53e4bd(0x226)](_0x53e4bd(0x251)+this['_webSocketErrorDocsLink']),_0x272872(new Error(_0x53e4bd(0x174)+(_0x369a11&&_0x369a11['message'])))));}));}[_0x306708(0x1ea)](_0x105214){var _0x110a37=_0x306708;this[_0x110a37(0x214)]=!0x1,this[_0x110a37(0x189)]=!0x1;try{_0x105214[_0x110a37(0x1d2)]=null,_0x105214[_0x110a37(0x197)]=null,_0x105214[_0x110a37(0x216)]=null;}catch{}try{_0x105214[_0x110a37(0x208)]<0x2&&_0x105214[_0x110a37(0x199)]();}catch{}}['_attemptToReconnectShortly'](){var _0x4578ee=_0x306708;clearTimeout(this[_0x4578ee(0x1c0)]),!(this['_connectAttemptCount']>=this[_0x4578ee(0x1f7)])&&(this[_0x4578ee(0x1c0)]=setTimeout(()=>{var _0x51f9fb=_0x4578ee,_0x2704fd;this['_connected']||this[_0x51f9fb(0x189)]||(this[_0x51f9fb(0x17e)](),(_0x2704fd=this[_0x51f9fb(0x26c)])==null||_0x2704fd[_0x51f9fb(0x240)](()=>this[_0x51f9fb(0x227)]()));},0x1f4),this[_0x4578ee(0x1c0)]['unref']&&this[_0x4578ee(0x1c0)]['unref']());}async[_0x306708(0x1c6)](_0x4ac647){var _0x5b6043=_0x306708;try{if(!this['_allowedToSend'])return;this[_0x5b6043(0x1e5)]&&this[_0x5b6043(0x17e)](),(await this[_0x5b6043(0x26c)])['send'](JSON[_0x5b6043(0x180)](_0x4ac647));}catch(_0x4af89c){this[_0x5b6043(0x221)]?console[_0x5b6043(0x226)](this['_sendErrorMessage']+':\\\\x20'+(_0x4af89c&&_0x4af89c[_0x5b6043(0x203)])):(this['_extendedWarning']=!0x0,console['warn'](this[_0x5b6043(0x1bc)]+':\\\\x20'+(_0x4af89c&&_0x4af89c['message']),_0x4ac647)),this['_allowedToSend']=!0x1,this[_0x5b6043(0x227)]();}}};function X(_0x59e9ed,_0x435000,_0x46a0b4,_0x387ea1,_0x3d0ee1,_0x388879,_0x3b442d,_0x3a7591=ae){var _0x316efd=_0x306708;let _0x5e24ec=_0x46a0b4[_0x316efd(0x17c)](',')['map'](_0x2205c0=>{var _0x2a4a71=_0x316efd,_0x289000,_0x38e841,_0x33274a,_0x1654bd;try{if(!_0x59e9ed[_0x2a4a71(0x24d)]){let _0x376a4e=((_0x38e841=(_0x289000=_0x59e9ed[_0x2a4a71(0x25e)])==null?void 0x0:_0x289000['versions'])==null?void 0x0:_0x38e841[_0x2a4a71(0x198)])||((_0x1654bd=(_0x33274a=_0x59e9ed[_0x2a4a71(0x25e)])==null?void 0x0:_0x33274a[_0x2a4a71(0x1ff)])==null?void 0x0:_0x1654bd[_0x2a4a71(0x1d8)])===_0x2a4a71(0x185);(_0x3d0ee1==='next.js'||_0x3d0ee1===_0x2a4a71(0x1e4)||_0x3d0ee1===_0x2a4a71(0x22f)||_0x3d0ee1===_0x2a4a71(0x213))&&(_0x3d0ee1+=_0x376a4e?_0x2a4a71(0x1b3):'\\\\x20browser'),_0x59e9ed[_0x2a4a71(0x24d)]={'id':+new Date(),'tool':_0x3d0ee1},_0x3b442d&&_0x3d0ee1&&!_0x376a4e&&console['log'](_0x2a4a71(0x264)+(_0x3d0ee1['charAt'](0x0)[_0x2a4a71(0x238)]()+_0x3d0ee1[_0x2a4a71(0x193)](0x1))+',',_0x2a4a71(0x1f5),_0x2a4a71(0x16f));}let _0x3065a6=new H(_0x59e9ed,_0x435000,_0x2205c0,_0x387ea1,_0x388879,_0x3a7591);return _0x3065a6[_0x2a4a71(0x1c6)]['bind'](_0x3065a6);}catch(_0x5cbdc5){return console['warn'](_0x2a4a71(0x247),_0x5cbdc5&&_0x5cbdc5['message']),()=>{};}});return _0x59f8b9=>_0x5e24ec[_0x316efd(0x22d)](_0x5155f2=>_0x5155f2(_0x59f8b9));}function ae(_0x32c035,_0x386228,_0x1ba55a,_0x3db203){var _0xcccd91=_0x306708;_0x3db203&&_0x32c035===_0xcccd91(0x1dd)&&_0x1ba55a[_0xcccd91(0x17d)]['reload']();}function _0x57eb(_0x55b34c,_0x3b16e9){var _0x4ac419=_0x4ac4();return _0x57eb=function(_0x57eb0e,_0x21c986){_0x57eb0e=_0x57eb0e-0x167;var _0x1c6b34=_0x4ac419[_0x57eb0e];return _0x1c6b34;},_0x57eb(_0x55b34c,_0x3b16e9);}function B(_0x3d2273){var _0x4c09a6=_0x306708,_0x1bd3ca,_0x70cdff;let _0x3131bb=function(_0x4828d9,_0x331191){return _0x331191-_0x4828d9;},_0x121f61;if(_0x3d2273[_0x4c09a6(0x24b)])_0x121f61=function(){var _0x29e0e2=_0x4c09a6;return _0x3d2273[_0x29e0e2(0x24b)][_0x29e0e2(0x1fb)]();};else{if(_0x3d2273[_0x4c09a6(0x25e)]&&_0x3d2273[_0x4c09a6(0x25e)][_0x4c09a6(0x190)]&&((_0x70cdff=(_0x1bd3ca=_0x3d2273[_0x4c09a6(0x25e)])==null?void 0x0:_0x1bd3ca['env'])==null?void 0x0:_0x70cdff[_0x4c09a6(0x1d8)])!==_0x4c09a6(0x185))_0x121f61=function(){var _0x5b8216=_0x4c09a6;return _0x3d2273[_0x5b8216(0x25e)]['hrtime']();},_0x3131bb=function(_0x174a25,_0x4a360c){return 0x3e8*(_0x4a360c[0x0]-_0x174a25[0x0])+(_0x4a360c[0x1]-_0x174a25[0x1])/0xf4240;};else try{let {performance:_0x1423c5}=require(_0x4c09a6(0x26f));_0x121f61=function(){var _0x322a81=_0x4c09a6;return _0x1423c5[_0x322a81(0x1fb)]();};}catch{_0x121f61=function(){return+new Date();};}}return{'elapsed':_0x3131bb,'timeStamp':_0x121f61,'now':()=>Date[_0x4c09a6(0x1fb)]()};}function J(_0x42b318,_0x19743b,_0x2fa843){var _0x48a4a0=_0x306708,_0x164059,_0x17f315,_0x43da7c,_0x1142c4,_0x943066;if(_0x42b318[_0x48a4a0(0x171)]!==void 0x0)return _0x42b318[_0x48a4a0(0x171)];let _0x242317=((_0x17f315=(_0x164059=_0x42b318[_0x48a4a0(0x25e)])==null?void 0x0:_0x164059[_0x48a4a0(0x263)])==null?void 0x0:_0x17f315[_0x48a4a0(0x198)])||((_0x1142c4=(_0x43da7c=_0x42b318[_0x48a4a0(0x25e)])==null?void 0x0:_0x43da7c[_0x48a4a0(0x1ff)])==null?void 0x0:_0x1142c4[_0x48a4a0(0x1d8)])==='edge';function _0x1a94f0(_0x586890){var _0x1f649e=_0x48a4a0;if(_0x586890['startsWith']('/')&&_0x586890[_0x1f649e(0x1cf)]('/')){let _0x3e6dd8=new RegExp(_0x586890['slice'](0x1,-0x1));return _0x594d13=>_0x3e6dd8[_0x1f649e(0x1b7)](_0x594d13);}else{if(_0x586890[_0x1f649e(0x241)]('*')||_0x586890[_0x1f649e(0x241)]('?')){let _0x2ebcb8=new RegExp('^'+_0x586890[_0x1f649e(0x220)](/\\\\./g,String[_0x1f649e(0x204)](0x5c)+'.')[_0x1f649e(0x220)](/\\\\*/g,'.*')[_0x1f649e(0x220)](/\\\\?/g,'.')+String[_0x1f649e(0x204)](0x24));return _0x5dcdaa=>_0x2ebcb8[_0x1f649e(0x1b7)](_0x5dcdaa);}else return _0x135db6=>_0x135db6===_0x586890;}}let _0xe3393d=_0x19743b[_0x48a4a0(0x1e1)](_0x1a94f0);return _0x42b318[_0x48a4a0(0x171)]=_0x242317||!_0x19743b,!_0x42b318[_0x48a4a0(0x171)]&&((_0x943066=_0x42b318[_0x48a4a0(0x17d)])==null?void 0x0:_0x943066[_0x48a4a0(0x1bf)])&&(_0x42b318[_0x48a4a0(0x171)]=_0xe3393d['some'](_0x548db0=>_0x548db0(_0x42b318[_0x48a4a0(0x17d)]['hostname']))),_0x42b318[_0x48a4a0(0x171)];}function Y(_0x3fa09f,_0x44ab87,_0x12e5a6,_0x24945e,_0x3b9fc4){var _0x1a0229=_0x306708;_0x3fa09f=_0x3fa09f,_0x44ab87=_0x44ab87,_0x12e5a6=_0x12e5a6,_0x24945e=_0x24945e,_0x3b9fc4=_0x3b9fc4,_0x3b9fc4=_0x3b9fc4||{},_0x3b9fc4['defaultLimits']=_0x3b9fc4[_0x1a0229(0x223)]||{},_0x3b9fc4['reducedLimits']=_0x3b9fc4[_0x1a0229(0x1cd)]||{},_0x3b9fc4[_0x1a0229(0x259)]=_0x3b9fc4[_0x1a0229(0x259)]||{},_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x18d)]=_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x18d)]||{},_0x3b9fc4[_0x1a0229(0x259)]['global']=_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x1be)]||{};let _0x151ea4={'perLogpoint':{'reduceOnCount':_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x18d)][_0x1a0229(0x237)]||0x32,'reduceOnAccumulatedProcessingTimeMs':_0x3b9fc4[_0x1a0229(0x259)]['perLogpoint']['reduceOnAccumulatedProcessingTimeMs']||0x64,'resetWhenQuietMs':_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x18d)][_0x1a0229(0x1f8)]||0x1f4,'resetOnProcessingTimeAverageMs':_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x18d)][_0x1a0229(0x19c)]||0x64},'global':{'reduceOnCount':_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x1be)][_0x1a0229(0x237)]||0x3e8,'reduceOnAccumulatedProcessingTimeMs':_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x1be)][_0x1a0229(0x1e9)]||0x12c,'resetWhenQuietMs':_0x3b9fc4['reducePolicy'][_0x1a0229(0x1be)][_0x1a0229(0x1f8)]||0x32,'resetOnProcessingTimeAverageMs':_0x3b9fc4[_0x1a0229(0x259)][_0x1a0229(0x1be)][_0x1a0229(0x19c)]||0x64}},_0x2633ed=B(_0x3fa09f),_0x3e3008=_0x2633ed[_0x1a0229(0x1ca)],_0xac187d=_0x2633ed[_0x1a0229(0x22e)];class _0x111409{constructor(){var _0x179f3c=_0x1a0229;this[_0x179f3c(0x1eb)]=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x179f3c(0x1d5)]=/^(0|[1-9][0-9]*)$/,this[_0x179f3c(0x218)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x179f3c(0x268)]=_0x3fa09f[_0x179f3c(0x20a)],this[_0x179f3c(0x24c)]=_0x3fa09f['HTMLAllCollection'],this[_0x179f3c(0x179)]=Object[_0x179f3c(0x1f2)],this[_0x179f3c(0x205)]=Object['getOwnPropertyNames'],this['_Symbol']=_0x3fa09f[_0x179f3c(0x18e)],this[_0x179f3c(0x265)]=RegExp[_0x179f3c(0x269)]['toString'],this[_0x179f3c(0x17a)]=Date['prototype'][_0x179f3c(0x252)];}['serialize'](_0x40d6a8,_0x554cac,_0x52039e,_0xacf72f){var _0x57ff23=_0x1a0229,_0x27498f=this,_0x3d1269=_0x52039e[_0x57ff23(0x1a4)];function _0x352c7e(_0x319167,_0x1cb4b8,_0x46d708){var _0x25615d=_0x57ff23;_0x1cb4b8[_0x25615d(0x21d)]='unknown',_0x1cb4b8[_0x25615d(0x195)]=_0x319167['message'],_0x3294e4=_0x46d708[_0x25615d(0x198)][_0x25615d(0x24f)],_0x46d708[_0x25615d(0x198)][_0x25615d(0x24f)]=_0x1cb4b8,_0x27498f[_0x25615d(0x1c1)](_0x1cb4b8,_0x46d708);}let _0x203bc0;_0x3fa09f[_0x57ff23(0x1ba)]&&(_0x203bc0=_0x3fa09f[_0x57ff23(0x1ba)]['error'],_0x203bc0&&(_0x3fa09f[_0x57ff23(0x1ba)][_0x57ff23(0x195)]=function(){}));try{try{_0x52039e[_0x57ff23(0x233)]++,_0x52039e[_0x57ff23(0x1a4)]&&_0x52039e[_0x57ff23(0x1cb)]['push'](_0x554cac);var _0x507685,_0x2b5a2a,_0x5a02a3,_0x5cc2d1,_0x191590=[],_0x5d6631=[],_0xeb0b4,_0x430667=this[_0x57ff23(0x20e)](_0x554cac),_0x5ec1b1=_0x430667===_0x57ff23(0x1ee),_0x9c9d40=!0x1,_0x359db7=_0x430667===_0x57ff23(0x1fa),_0x55f196=this[_0x57ff23(0x20f)](_0x430667),_0x1c958d=this[_0x57ff23(0x26d)](_0x430667),_0x26f774=_0x55f196||_0x1c958d,_0x28c67b={},_0x34603f=0x0,_0x22ac07=!0x1,_0x3294e4,_0x468afa=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x52039e['depth']){if(_0x5ec1b1){if(_0x2b5a2a=_0x554cac[_0x57ff23(0x1b6)],_0x2b5a2a>_0x52039e[_0x57ff23(0x16d)]){for(_0x5a02a3=0x0,_0x5cc2d1=_0x52039e[_0x57ff23(0x16d)],_0x507685=_0x5a02a3;_0x507685<_0x5cc2d1;_0x507685++)_0x5d6631[_0x57ff23(0x26e)](_0x27498f[_0x57ff23(0x20b)](_0x191590,_0x554cac,_0x430667,_0x507685,_0x52039e));_0x40d6a8[_0x57ff23(0x1d1)]=!0x0;}else{for(_0x5a02a3=0x0,_0x5cc2d1=_0x2b5a2a,_0x507685=_0x5a02a3;_0x507685<_0x5cc2d1;_0x507685++)_0x5d6631[_0x57ff23(0x26e)](_0x27498f[_0x57ff23(0x20b)](_0x191590,_0x554cac,_0x430667,_0x507685,_0x52039e));}_0x52039e[_0x57ff23(0x19e)]+=_0x5d6631[_0x57ff23(0x1b6)];}if(!(_0x430667===_0x57ff23(0x1d9)||_0x430667===_0x57ff23(0x20a))&&!_0x55f196&&_0x430667!=='String'&&_0x430667!==_0x57ff23(0x261)&&_0x430667!==_0x57ff23(0x230)){var _0x48cb10=_0xacf72f[_0x57ff23(0x18f)]||_0x52039e['props'];if(this[_0x57ff23(0x194)](_0x554cac)?(_0x507685=0x0,_0x554cac[_0x57ff23(0x22d)](function(_0x5e3879){var _0x4028da=_0x57ff23;if(_0x34603f++,_0x52039e[_0x4028da(0x19e)]++,_0x34603f>_0x48cb10){_0x22ac07=!0x0;return;}if(!_0x52039e[_0x4028da(0x1b4)]&&_0x52039e['autoExpand']&&_0x52039e[_0x4028da(0x19e)]>_0x52039e[_0x4028da(0x1a3)]){_0x22ac07=!0x0;return;}_0x5d6631['push'](_0x27498f[_0x4028da(0x20b)](_0x191590,_0x554cac,'Set',_0x507685++,_0x52039e,function(_0x326cff){return function(){return _0x326cff;};}(_0x5e3879)));})):this['_isMap'](_0x554cac)&&_0x554cac[_0x57ff23(0x22d)](function(_0x21d5e9,_0x1bbf3a){var _0x572ec4=_0x57ff23;if(_0x34603f++,_0x52039e[_0x572ec4(0x19e)]++,_0x34603f>_0x48cb10){_0x22ac07=!0x0;return;}if(!_0x52039e[_0x572ec4(0x1b4)]&&_0x52039e[_0x572ec4(0x1a4)]&&_0x52039e['autoExpandPropertyCount']>_0x52039e['autoExpandLimit']){_0x22ac07=!0x0;return;}var _0x1e13a8=_0x1bbf3a[_0x572ec4(0x252)]();_0x1e13a8[_0x572ec4(0x1b6)]>0x64&&(_0x1e13a8=_0x1e13a8[_0x572ec4(0x16c)](0x0,0x64)+_0x572ec4(0x19f)),_0x5d6631[_0x572ec4(0x26e)](_0x27498f[_0x572ec4(0x20b)](_0x191590,_0x554cac,'Map',_0x1e13a8,_0x52039e,function(_0x5e237b){return function(){return _0x5e237b;};}(_0x21d5e9)));}),!_0x9c9d40){try{for(_0xeb0b4 in _0x554cac)if(!(_0x5ec1b1&&_0x468afa[_0x57ff23(0x1b7)](_0xeb0b4))&&!this[_0x57ff23(0x267)](_0x554cac,_0xeb0b4,_0x52039e)){if(_0x34603f++,_0x52039e[_0x57ff23(0x19e)]++,_0x34603f>_0x48cb10){_0x22ac07=!0x0;break;}if(!_0x52039e[_0x57ff23(0x1b4)]&&_0x52039e[_0x57ff23(0x1a4)]&&_0x52039e['autoExpandPropertyCount']>_0x52039e['autoExpandLimit']){_0x22ac07=!0x0;break;}_0x5d6631[_0x57ff23(0x26e)](_0x27498f[_0x57ff23(0x234)](_0x191590,_0x28c67b,_0x554cac,_0x430667,_0xeb0b4,_0x52039e));}}catch{}if(_0x28c67b[_0x57ff23(0x173)]=!0x0,_0x359db7&&(_0x28c67b[_0x57ff23(0x1b5)]=!0x0),!_0x22ac07){var _0xb9ad21=[][_0x57ff23(0x256)](this[_0x57ff23(0x205)](_0x554cac))[_0x57ff23(0x256)](this['_getOwnPropertySymbols'](_0x554cac));for(_0x507685=0x0,_0x2b5a2a=_0xb9ad21[_0x57ff23(0x1b6)];_0x507685<_0x2b5a2a;_0x507685++)if(_0xeb0b4=_0xb9ad21[_0x507685],!(_0x5ec1b1&&_0x468afa[_0x57ff23(0x1b7)](_0xeb0b4[_0x57ff23(0x252)]()))&&!this[_0x57ff23(0x267)](_0x554cac,_0xeb0b4,_0x52039e)&&!_0x28c67b[_0x57ff23(0x1ab)+_0xeb0b4[_0x57ff23(0x252)]()]){if(_0x34603f++,_0x52039e[_0x57ff23(0x19e)]++,_0x34603f>_0x48cb10){_0x22ac07=!0x0;break;}if(!_0x52039e[_0x57ff23(0x1b4)]&&_0x52039e[_0x57ff23(0x1a4)]&&_0x52039e[_0x57ff23(0x19e)]>_0x52039e['autoExpandLimit']){_0x22ac07=!0x0;break;}_0x5d6631['push'](_0x27498f['_addObjectProperty'](_0x191590,_0x28c67b,_0x554cac,_0x430667,_0xeb0b4,_0x52039e));}}}}}if(_0x40d6a8['type']=_0x430667,_0x26f774?(_0x40d6a8[_0x57ff23(0x184)]=_0x554cac['valueOf'](),this[_0x57ff23(0x1e7)](_0x430667,_0x40d6a8,_0x52039e,_0xacf72f)):_0x430667==='date'?_0x40d6a8[_0x57ff23(0x184)]=this[_0x57ff23(0x17a)][_0x57ff23(0x21a)](_0x554cac):_0x430667===_0x57ff23(0x230)?_0x40d6a8[_0x57ff23(0x184)]=_0x554cac[_0x57ff23(0x252)]():_0x430667===_0x57ff23(0x244)?_0x40d6a8[_0x57ff23(0x184)]=this[_0x57ff23(0x265)][_0x57ff23(0x21a)](_0x554cac):_0x430667===_0x57ff23(0x1dc)&&this['_Symbol']?_0x40d6a8[_0x57ff23(0x184)]=this[_0x57ff23(0x1f9)]['prototype'][_0x57ff23(0x252)][_0x57ff23(0x21a)](_0x554cac):!_0x52039e[_0x57ff23(0x1fe)]&&!(_0x430667===_0x57ff23(0x1d9)||_0x430667==='undefined')&&(delete _0x40d6a8[_0x57ff23(0x184)],_0x40d6a8[_0x57ff23(0x1aa)]=!0x0),_0x22ac07&&(_0x40d6a8[_0x57ff23(0x19d)]=!0x0),_0x3294e4=_0x52039e['node'][_0x57ff23(0x24f)],_0x52039e['node'][_0x57ff23(0x24f)]=_0x40d6a8,this[_0x57ff23(0x1c1)](_0x40d6a8,_0x52039e),_0x5d6631[_0x57ff23(0x1b6)]){for(_0x507685=0x0,_0x2b5a2a=_0x5d6631[_0x57ff23(0x1b6)];_0x507685<_0x2b5a2a;_0x507685++)_0x5d6631[_0x507685](_0x507685);}_0x191590[_0x57ff23(0x1b6)]&&(_0x40d6a8[_0x57ff23(0x18f)]=_0x191590);}catch(_0x3e590f){_0x352c7e(_0x3e590f,_0x40d6a8,_0x52039e);}this[_0x57ff23(0x1ad)](_0x554cac,_0x40d6a8),this[_0x57ff23(0x1f3)](_0x40d6a8,_0x52039e),_0x52039e[_0x57ff23(0x198)][_0x57ff23(0x24f)]=_0x3294e4,_0x52039e[_0x57ff23(0x233)]--,_0x52039e[_0x57ff23(0x1a4)]=_0x3d1269,_0x52039e[_0x57ff23(0x1a4)]&&_0x52039e[_0x57ff23(0x1cb)][_0x57ff23(0x21b)]();}finally{_0x203bc0&&(_0x3fa09f[_0x57ff23(0x1ba)][_0x57ff23(0x195)]=_0x203bc0);}return _0x40d6a8;}[_0x1a0229(0x1a7)](_0x5bc7aa){return Object['getOwnPropertySymbols']?Object['getOwnPropertySymbols'](_0x5bc7aa):[];}['_isSet'](_0x2b9132){var _0x64ad7f=_0x1a0229;return!!(_0x2b9132&&_0x3fa09f[_0x64ad7f(0x1af)]&&this[_0x64ad7f(0x215)](_0x2b9132)===_0x64ad7f(0x168)&&_0x2b9132[_0x64ad7f(0x22d)]);}['_blacklistedProperty'](_0x6da349,_0x27887d,_0x3ab798){var _0x15f72a=_0x1a0229;if(!_0x3ab798[_0x15f72a(0x207)]){let _0x4b858a=this[_0x15f72a(0x179)](_0x6da349,_0x27887d);if(_0x4b858a&&_0x4b858a[_0x15f72a(0x1e8)])return!0x0;}return _0x3ab798['noFunctions']?typeof _0x6da349[_0x27887d]==_0x15f72a(0x1fa):!0x1;}[_0x1a0229(0x20e)](_0x46e341){var _0xed104=_0x1a0229,_0x842a19='';return _0x842a19=typeof _0x46e341,_0x842a19==='object'?this[_0xed104(0x215)](_0x46e341)==='[object\\\\x20Array]'?_0x842a19='array':this[_0xed104(0x215)](_0x46e341)===_0xed104(0x231)?_0x842a19=_0xed104(0x1a0):this[_0xed104(0x215)](_0x46e341)===_0xed104(0x25c)?_0x842a19='bigint':_0x46e341===null?_0x842a19='null':_0x46e341['constructor']&&(_0x842a19=_0x46e341[_0xed104(0x246)][_0xed104(0x229)]||_0x842a19):_0x842a19===_0xed104(0x20a)&&this[_0xed104(0x24c)]&&_0x46e341 instanceof this['_HTMLAllCollection']&&(_0x842a19=_0xed104(0x1b9)),_0x842a19;}[_0x1a0229(0x215)](_0x3a4a42){var _0x52978d=_0x1a0229;return Object[_0x52978d(0x269)][_0x52978d(0x252)][_0x52978d(0x21a)](_0x3a4a42);}[_0x1a0229(0x20f)](_0x344afd){var _0x18ea96=_0x1a0229;return _0x344afd===_0x18ea96(0x169)||_0x344afd===_0x18ea96(0x1e3)||_0x344afd===_0x18ea96(0x1b1);}[_0x1a0229(0x26d)](_0x268fb2){var _0x30d14e=_0x1a0229;return _0x268fb2===_0x30d14e(0x250)||_0x268fb2===_0x30d14e(0x1c2)||_0x268fb2===_0x30d14e(0x232);}[_0x1a0229(0x20b)](_0x5a4048,_0xed6d73,_0x496d2d,_0x5200b2,_0x2ad9ae,_0x2ae99d){var _0x3ea8a2=this;return function(_0x5147f2){var _0x5c347b=_0x57eb,_0x5a16fe=_0x2ad9ae[_0x5c347b(0x198)][_0x5c347b(0x24f)],_0x344d27=_0x2ad9ae['node'][_0x5c347b(0x211)],_0x4187de=_0x2ad9ae[_0x5c347b(0x198)][_0x5c347b(0x192)];_0x2ad9ae['node']['parent']=_0x5a16fe,_0x2ad9ae[_0x5c347b(0x198)][_0x5c347b(0x211)]=typeof _0x5200b2==_0x5c347b(0x1b1)?_0x5200b2:_0x5147f2,_0x5a4048[_0x5c347b(0x26e)](_0x3ea8a2['_property'](_0xed6d73,_0x496d2d,_0x5200b2,_0x2ad9ae,_0x2ae99d)),_0x2ad9ae['node'][_0x5c347b(0x192)]=_0x4187de,_0x2ad9ae[_0x5c347b(0x198)][_0x5c347b(0x211)]=_0x344d27;};}[_0x1a0229(0x234)](_0x3e1fde,_0x3d8d61,_0x475fe8,_0x5aec1a,_0x4ff186,_0x21a460,_0x5c688a){var _0x547a69=_0x1a0229,_0x5e16c3=this;return _0x3d8d61['_p_'+_0x4ff186[_0x547a69(0x252)]()]=!0x0,function(_0x176a2d){var _0x5d8009=_0x547a69,_0x5cf964=_0x21a460[_0x5d8009(0x198)][_0x5d8009(0x24f)],_0x3bc9c4=_0x21a460[_0x5d8009(0x198)][_0x5d8009(0x211)],_0x30229a=_0x21a460['node'][_0x5d8009(0x192)];_0x21a460[_0x5d8009(0x198)]['parent']=_0x5cf964,_0x21a460[_0x5d8009(0x198)][_0x5d8009(0x211)]=_0x176a2d,_0x3e1fde['push'](_0x5e16c3[_0x5d8009(0x23c)](_0x475fe8,_0x5aec1a,_0x4ff186,_0x21a460,_0x5c688a)),_0x21a460[_0x5d8009(0x198)][_0x5d8009(0x192)]=_0x30229a,_0x21a460[_0x5d8009(0x198)][_0x5d8009(0x211)]=_0x3bc9c4;};}[_0x1a0229(0x23c)](_0x1354ea,_0x549fc8,_0x34c184,_0x3ea67a,_0x187926){var _0x54c1c6=_0x1a0229,_0x517077=this;_0x187926||(_0x187926=function(_0x560c04,_0x358223){return _0x560c04[_0x358223];});var _0x4b95cc=_0x34c184[_0x54c1c6(0x252)](),_0x2df5ee=_0x3ea67a[_0x54c1c6(0x1e0)]||{},_0x27230e=_0x3ea67a[_0x54c1c6(0x1fe)],_0x58094a=_0x3ea67a[_0x54c1c6(0x1b4)];try{var _0x551f2a=this[_0x54c1c6(0x249)](_0x1354ea),_0x258e78=_0x4b95cc;_0x551f2a&&_0x258e78[0x0]==='\\\\x27'&&(_0x258e78=_0x258e78[_0x54c1c6(0x193)](0x1,_0x258e78[_0x54c1c6(0x1b6)]-0x2));var _0x167185=_0x3ea67a[_0x54c1c6(0x1e0)]=_0x2df5ee[_0x54c1c6(0x1ab)+_0x258e78];_0x167185&&(_0x3ea67a[_0x54c1c6(0x1fe)]=_0x3ea67a['depth']+0x1),_0x3ea67a[_0x54c1c6(0x1b4)]=!!_0x167185;var _0x5bb1a9=typeof _0x34c184==_0x54c1c6(0x1dc),_0x4b5ab0={'name':_0x5bb1a9||_0x551f2a?_0x4b95cc:this[_0x54c1c6(0x20d)](_0x4b95cc)};if(_0x5bb1a9&&(_0x4b5ab0['symbol']=!0x0),!(_0x549fc8===_0x54c1c6(0x1ee)||_0x549fc8===_0x54c1c6(0x242))){var _0x12e894=this[_0x54c1c6(0x179)](_0x1354ea,_0x34c184);if(_0x12e894&&(_0x12e894['set']&&(_0x4b5ab0[_0x54c1c6(0x1ce)]=!0x0),_0x12e894[_0x54c1c6(0x1e8)]&&!_0x167185&&!_0x3ea67a[_0x54c1c6(0x207)]))return _0x4b5ab0[_0x54c1c6(0x235)]=!0x0,this[_0x54c1c6(0x17b)](_0x4b5ab0,_0x3ea67a),_0x4b5ab0;}var _0x117dac;try{_0x117dac=_0x187926(_0x1354ea,_0x34c184);}catch(_0x552030){return _0x4b5ab0={'name':_0x4b95cc,'type':_0x54c1c6(0x1b0),'error':_0x552030[_0x54c1c6(0x203)]},this['_processTreeNodeResult'](_0x4b5ab0,_0x3ea67a),_0x4b5ab0;}var _0x353809=this[_0x54c1c6(0x20e)](_0x117dac),_0x585700=this[_0x54c1c6(0x20f)](_0x353809);if(_0x4b5ab0[_0x54c1c6(0x21d)]=_0x353809,_0x585700)this[_0x54c1c6(0x17b)](_0x4b5ab0,_0x3ea67a,_0x117dac,function(){var _0x44f506=_0x54c1c6;_0x4b5ab0['value']=_0x117dac['valueOf'](),!_0x167185&&_0x517077[_0x44f506(0x1e7)](_0x353809,_0x4b5ab0,_0x3ea67a,{});});else{var _0x4920c5=_0x3ea67a[_0x54c1c6(0x1a4)]&&_0x3ea67a[_0x54c1c6(0x233)]<_0x3ea67a[_0x54c1c6(0x1b8)]&&_0x3ea67a[_0x54c1c6(0x1cb)][_0x54c1c6(0x224)](_0x117dac)<0x0&&_0x353809!==_0x54c1c6(0x1fa)&&_0x3ea67a[_0x54c1c6(0x19e)]<_0x3ea67a['autoExpandLimit'];_0x4920c5||_0x3ea67a[_0x54c1c6(0x233)]<_0x27230e||_0x167185?(this['serialize'](_0x4b5ab0,_0x117dac,_0x3ea67a,_0x167185||{}),this[_0x54c1c6(0x1ad)](_0x117dac,_0x4b5ab0)):this['_processTreeNodeResult'](_0x4b5ab0,_0x3ea67a,_0x117dac,function(){var _0x57d325=_0x54c1c6;_0x353809===_0x57d325(0x1d9)||_0x353809===_0x57d325(0x20a)||(delete _0x4b5ab0[_0x57d325(0x184)],_0x4b5ab0['capped']=!0x0);});}return _0x4b5ab0;}finally{_0x3ea67a[_0x54c1c6(0x1e0)]=_0x2df5ee,_0x3ea67a[_0x54c1c6(0x1fe)]=_0x27230e,_0x3ea67a[_0x54c1c6(0x1b4)]=_0x58094a;}}[_0x1a0229(0x1e7)](_0x56ea77,_0xe68ac4,_0x19079c,_0x151ab0){var _0x4ce5e3=_0x1a0229,_0x3ddd62=_0x151ab0[_0x4ce5e3(0x1de)]||_0x19079c[_0x4ce5e3(0x1de)];if((_0x56ea77===_0x4ce5e3(0x1e3)||_0x56ea77===_0x4ce5e3(0x1c2))&&_0xe68ac4['value']){let _0x1e81f3=_0xe68ac4[_0x4ce5e3(0x184)][_0x4ce5e3(0x1b6)];_0x19079c['allStrLength']+=_0x1e81f3,_0x19079c[_0x4ce5e3(0x200)]>_0x19079c[_0x4ce5e3(0x1c7)]?(_0xe68ac4[_0x4ce5e3(0x1aa)]='',delete _0xe68ac4[_0x4ce5e3(0x184)]):_0x1e81f3>_0x3ddd62&&(_0xe68ac4[_0x4ce5e3(0x1aa)]=_0xe68ac4[_0x4ce5e3(0x184)]['substr'](0x0,_0x3ddd62),delete _0xe68ac4['value']);}}[_0x1a0229(0x249)](_0x394563){var _0x5a0544=_0x1a0229;return!!(_0x394563&&_0x3fa09f[_0x5a0544(0x1ec)]&&this[_0x5a0544(0x215)](_0x394563)===_0x5a0544(0x23a)&&_0x394563[_0x5a0544(0x22d)]);}[_0x1a0229(0x20d)](_0x182c1e){var _0x45e012=_0x1a0229;if(_0x182c1e[_0x45e012(0x1f4)](/^\\\\d+$/))return _0x182c1e;var _0x3a5b42;try{_0x3a5b42=JSON['stringify'](''+_0x182c1e);}catch{_0x3a5b42='\\\\x22'+this[_0x45e012(0x215)](_0x182c1e)+'\\\\x22';}return _0x3a5b42['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x3a5b42=_0x3a5b42[_0x45e012(0x193)](0x1,_0x3a5b42[_0x45e012(0x1b6)]-0x2):_0x3a5b42=_0x3a5b42[_0x45e012(0x220)](/'/g,'\\\\x5c\\\\x27')['replace'](/\\\\\\\\\\\"/g,'\\\\x22')['replace'](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x3a5b42;}[_0x1a0229(0x17b)](_0x42bdc4,_0x2913cb,_0x26c947,_0x1a1ce7){var _0x58d5f8=_0x1a0229;this[_0x58d5f8(0x1c1)](_0x42bdc4,_0x2913cb),_0x1a1ce7&&_0x1a1ce7(),this[_0x58d5f8(0x1ad)](_0x26c947,_0x42bdc4),this[_0x58d5f8(0x1f3)](_0x42bdc4,_0x2913cb);}[_0x1a0229(0x1c1)](_0x4e11a0,_0xbbeada){var _0x1a94cb=_0x1a0229;this[_0x1a94cb(0x181)](_0x4e11a0,_0xbbeada),this[_0x1a94cb(0x23e)](_0x4e11a0,_0xbbeada),this[_0x1a94cb(0x22a)](_0x4e11a0,_0xbbeada),this[_0x1a94cb(0x260)](_0x4e11a0,_0xbbeada);}[_0x1a0229(0x181)](_0x19de26,_0x41a25d){}[_0x1a0229(0x23e)](_0x1356cd,_0x4a5fac){}[_0x1a0229(0x1d0)](_0x1cd658,_0x4a1747){}[_0x1a0229(0x1f6)](_0x3cb148){var _0x43c24f=_0x1a0229;return _0x3cb148===this[_0x43c24f(0x268)];}[_0x1a0229(0x1f3)](_0x283677,_0x5afa0e){var _0x7f88c3=_0x1a0229;this[_0x7f88c3(0x1d0)](_0x283677,_0x5afa0e),this['_setNodeExpandableState'](_0x283677),_0x5afa0e[_0x7f88c3(0x1a9)]&&this[_0x7f88c3(0x1fd)](_0x283677),this[_0x7f88c3(0x1d4)](_0x283677,_0x5afa0e),this['_addLoadNode'](_0x283677,_0x5afa0e),this[_0x7f88c3(0x167)](_0x283677);}[_0x1a0229(0x1ad)](_0x13930e,_0x569f4b){var _0x488226=_0x1a0229;try{_0x13930e&&typeof _0x13930e[_0x488226(0x1b6)]==_0x488226(0x1b1)&&(_0x569f4b[_0x488226(0x1b6)]=_0x13930e[_0x488226(0x1b6)]);}catch{}if(_0x569f4b['type']===_0x488226(0x1b1)||_0x569f4b[_0x488226(0x21d)]===_0x488226(0x232)){if(isNaN(_0x569f4b[_0x488226(0x184)]))_0x569f4b[_0x488226(0x1df)]=!0x0,delete _0x569f4b[_0x488226(0x184)];else switch(_0x569f4b[_0x488226(0x184)]){case Number[_0x488226(0x236)]:_0x569f4b[_0x488226(0x239)]=!0x0,delete _0x569f4b['value'];break;case Number[_0x488226(0x228)]:_0x569f4b[_0x488226(0x1fc)]=!0x0,delete _0x569f4b['value'];break;case 0x0:this[_0x488226(0x19b)](_0x569f4b[_0x488226(0x184)])&&(_0x569f4b[_0x488226(0x178)]=!0x0);break;}}else _0x569f4b[_0x488226(0x21d)]===_0x488226(0x1fa)&&typeof _0x13930e['name']==_0x488226(0x1e3)&&_0x13930e[_0x488226(0x229)]&&_0x569f4b[_0x488226(0x229)]&&_0x13930e['name']!==_0x569f4b['name']&&(_0x569f4b['funcName']=_0x13930e[_0x488226(0x229)]);}[_0x1a0229(0x19b)](_0x2e602c){var _0x3d950b=_0x1a0229;return 0x1/_0x2e602c===Number[_0x3d950b(0x228)];}[_0x1a0229(0x1fd)](_0x429dd2){var _0x312c75=_0x1a0229;!_0x429dd2[_0x312c75(0x18f)]||!_0x429dd2['props'][_0x312c75(0x1b6)]||_0x429dd2[_0x312c75(0x21d)]===_0x312c75(0x1ee)||_0x429dd2[_0x312c75(0x21d)]==='Map'||_0x429dd2[_0x312c75(0x21d)]===_0x312c75(0x1af)||_0x429dd2[_0x312c75(0x18f)]['sort'](function(_0x612321,_0x1df7f8){var _0x3c5dab=_0x312c75,_0x1fba1b=_0x612321[_0x3c5dab(0x229)][_0x3c5dab(0x1ed)](),_0x1143f9=_0x1df7f8[_0x3c5dab(0x229)][_0x3c5dab(0x1ed)]();return _0x1fba1b<_0x1143f9?-0x1:_0x1fba1b>_0x1143f9?0x1:0x0;});}[_0x1a0229(0x1d4)](_0x3895f9,_0x48aadd){var _0x351d89=_0x1a0229;if(!(_0x48aadd['noFunctions']||!_0x3895f9[_0x351d89(0x18f)]||!_0x3895f9['props'][_0x351d89(0x1b6)])){for(var _0x4d9e5c=[],_0x23eb96=[],_0xd62b75=0x0,_0x5a2dfe=_0x3895f9[_0x351d89(0x18f)][_0x351d89(0x1b6)];_0xd62b75<_0x5a2dfe;_0xd62b75++){var _0x4c17b7=_0x3895f9[_0x351d89(0x18f)][_0xd62b75];_0x4c17b7[_0x351d89(0x21d)]===_0x351d89(0x1fa)?_0x4d9e5c[_0x351d89(0x26e)](_0x4c17b7):_0x23eb96[_0x351d89(0x26e)](_0x4c17b7);}if(!(!_0x23eb96[_0x351d89(0x1b6)]||_0x4d9e5c[_0x351d89(0x1b6)]<=0x1)){_0x3895f9[_0x351d89(0x18f)]=_0x23eb96;var _0x2b33a7={'functionsNode':!0x0,'props':_0x4d9e5c};this[_0x351d89(0x181)](_0x2b33a7,_0x48aadd),this['_setNodeLabel'](_0x2b33a7,_0x48aadd),this[_0x351d89(0x1ae)](_0x2b33a7),this[_0x351d89(0x260)](_0x2b33a7,_0x48aadd),_0x2b33a7['id']+='\\\\x20f',_0x3895f9[_0x351d89(0x18f)]['unshift'](_0x2b33a7);}}}[_0x1a0229(0x243)](_0x4abe08,_0x5b7c75){}['_setNodeExpandableState'](_0x59678d){}[_0x1a0229(0x212)](_0xdf985c){var _0x2524e2=_0x1a0229;return Array[_0x2524e2(0x23d)](_0xdf985c)||typeof _0xdf985c==_0x2524e2(0x206)&&this[_0x2524e2(0x215)](_0xdf985c)==='[object\\\\x20Array]';}['_setNodePermissions'](_0x3fb9a6,_0x4e6343){}['_cleanNode'](_0x23c336){var _0x1d7070=_0x1a0229;delete _0x23c336[_0x1d7070(0x253)],delete _0x23c336[_0x1d7070(0x1e2)],delete _0x23c336[_0x1d7070(0x18b)];}[_0x1a0229(0x22a)](_0x1acfab,_0x4597d6){}}let _0x3c89ee=new _0x111409(),_0x5ec458={'props':_0x3b9fc4[_0x1a0229(0x223)][_0x1a0229(0x18f)]||0x64,'elements':_0x3b9fc4[_0x1a0229(0x223)][_0x1a0229(0x16d)]||0x64,'strLength':_0x3b9fc4[_0x1a0229(0x223)][_0x1a0229(0x1de)]||0x400*0x32,'totalStrLength':_0x3b9fc4['defaultLimits'][_0x1a0229(0x1c7)]||0x400*0x32,'autoExpandLimit':_0x3b9fc4[_0x1a0229(0x223)][_0x1a0229(0x1a3)]||0x1388,'autoExpandMaxDepth':_0x3b9fc4[_0x1a0229(0x223)][_0x1a0229(0x1b8)]||0xa},_0x3a09dd={'props':_0x3b9fc4[_0x1a0229(0x1cd)][_0x1a0229(0x18f)]||0x5,'elements':_0x3b9fc4[_0x1a0229(0x1cd)][_0x1a0229(0x16d)]||0x5,'strLength':_0x3b9fc4[_0x1a0229(0x1cd)][_0x1a0229(0x1de)]||0x100,'totalStrLength':_0x3b9fc4['reducedLimits'][_0x1a0229(0x1c7)]||0x100*0x3,'autoExpandLimit':_0x3b9fc4['reducedLimits']['autoExpandLimit']||0x1e,'autoExpandMaxDepth':_0x3b9fc4[_0x1a0229(0x1cd)][_0x1a0229(0x1b8)]||0x2};function _0x4b3a63(_0x205bfc,_0x18b986,_0x503f06,_0x274aab,_0x31f6c4,_0x454f1c){var _0x290454=_0x1a0229;let _0x2396c5,_0x2aa929;try{_0x2aa929=_0xac187d(),_0x2396c5=_0x12e5a6[_0x18b986],!_0x2396c5||_0x2aa929-_0x2396c5['ts']>_0x151ea4['perLogpoint'][_0x290454(0x1f8)]&&_0x2396c5[_0x290454(0x1d6)]&&_0x2396c5[_0x290454(0x26a)]/_0x2396c5[_0x290454(0x1d6)]<_0x151ea4[_0x290454(0x18d)]['resetOnProcessingTimeAverageMs']?(_0x12e5a6[_0x18b986]=_0x2396c5={'count':0x0,'time':0x0,'ts':_0x2aa929},_0x12e5a6[_0x290454(0x257)]={}):_0x2aa929-_0x12e5a6[_0x290454(0x257)]['ts']>_0x151ea4[_0x290454(0x1be)][_0x290454(0x1f8)]&&_0x12e5a6[_0x290454(0x257)][_0x290454(0x1d6)]&&_0x12e5a6['hits'][_0x290454(0x26a)]/_0x12e5a6['hits'][_0x290454(0x1d6)]<_0x151ea4['global'][_0x290454(0x19c)]&&(_0x12e5a6[_0x290454(0x257)]={});let _0x465570=[],_0x401ade=_0x2396c5['reduceLimits']||_0x12e5a6[_0x290454(0x257)][_0x290454(0x1f1)]?_0x3a09dd:_0x5ec458,_0x5527b0=_0x17275e=>{var _0x16c392=_0x290454;let _0x3d2b1a={};return _0x3d2b1a[_0x16c392(0x18f)]=_0x17275e[_0x16c392(0x18f)],_0x3d2b1a[_0x16c392(0x16d)]=_0x17275e[_0x16c392(0x16d)],_0x3d2b1a[_0x16c392(0x1de)]=_0x17275e[_0x16c392(0x1de)],_0x3d2b1a['totalStrLength']=_0x17275e[_0x16c392(0x1c7)],_0x3d2b1a['autoExpandLimit']=_0x17275e[_0x16c392(0x1a3)],_0x3d2b1a[_0x16c392(0x1b8)]=_0x17275e[_0x16c392(0x1b8)],_0x3d2b1a[_0x16c392(0x1a9)]=!0x1,_0x3d2b1a[_0x16c392(0x175)]=!_0x44ab87,_0x3d2b1a[_0x16c392(0x1fe)]=0x1,_0x3d2b1a[_0x16c392(0x233)]=0x0,_0x3d2b1a[_0x16c392(0x176)]=_0x16c392(0x24a),_0x3d2b1a[_0x16c392(0x1e6)]=_0x16c392(0x18c),_0x3d2b1a[_0x16c392(0x1a4)]=!0x0,_0x3d2b1a[_0x16c392(0x1cb)]=[],_0x3d2b1a[_0x16c392(0x19e)]=0x0,_0x3d2b1a[_0x16c392(0x207)]=_0x3b9fc4['resolveGetters'],_0x3d2b1a[_0x16c392(0x200)]=0x0,_0x3d2b1a[_0x16c392(0x198)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x3d2b1a;};for(var _0x7c60bb=0x0;_0x7c60bb<_0x31f6c4[_0x290454(0x1b6)];_0x7c60bb++)_0x465570['push'](_0x3c89ee[_0x290454(0x245)]({'timeNode':_0x205bfc===_0x290454(0x26a)||void 0x0},_0x31f6c4[_0x7c60bb],_0x5527b0(_0x401ade),{}));if(_0x205bfc===_0x290454(0x255)||_0x205bfc===_0x290454(0x195)){let _0x37f42a=Error[_0x290454(0x188)];try{Error[_0x290454(0x188)]=0x1/0x0,_0x465570[_0x290454(0x26e)](_0x3c89ee[_0x290454(0x245)]({'stackNode':!0x0},new Error()[_0x290454(0x217)],_0x5527b0(_0x401ade),{'strLength':0x1/0x0}));}finally{Error[_0x290454(0x188)]=_0x37f42a;}}return{'method':_0x290454(0x186),'version':_0x24945e,'args':[{'ts':_0x503f06,'session':_0x274aab,'args':_0x465570,'id':_0x18b986,'context':_0x454f1c}]};}catch(_0x4d9d42){return{'method':'log','version':_0x24945e,'args':[{'ts':_0x503f06,'session':_0x274aab,'args':[{'type':'unknown','error':_0x4d9d42&&_0x4d9d42[_0x290454(0x203)]}],'id':_0x18b986,'context':_0x454f1c}]};}finally{try{if(_0x2396c5&&_0x2aa929){let _0x120976=_0xac187d();_0x2396c5[_0x290454(0x1d6)]++,_0x2396c5['time']+=_0x3e3008(_0x2aa929,_0x120976),_0x2396c5['ts']=_0x120976,_0x12e5a6['hits'][_0x290454(0x1d6)]++,_0x12e5a6['hits'][_0x290454(0x26a)]+=_0x3e3008(_0x2aa929,_0x120976),_0x12e5a6[_0x290454(0x257)]['ts']=_0x120976,(_0x2396c5[_0x290454(0x1d6)]>_0x151ea4[_0x290454(0x18d)][_0x290454(0x237)]||_0x2396c5['time']>_0x151ea4[_0x290454(0x18d)]['reduceOnAccumulatedProcessingTimeMs'])&&(_0x2396c5[_0x290454(0x1f1)]=!0x0),(_0x12e5a6[_0x290454(0x257)][_0x290454(0x1d6)]>_0x151ea4[_0x290454(0x1be)][_0x290454(0x237)]||_0x12e5a6[_0x290454(0x257)][_0x290454(0x26a)]>_0x151ea4[_0x290454(0x1be)][_0x290454(0x1e9)])&&(_0x12e5a6['hits'][_0x290454(0x1f1)]=!0x0);}}catch{}}}return _0x4b3a63;}((_0x128c8b,_0x130c59,_0x2d3050,_0xddf41c,_0x441d00,_0x1b84a8,_0x2217b2,_0x5b051f,_0x2b48ca,_0x490710,_0x5ab68a,_0xebfbce)=>{var _0x48efd0=_0x306708;if(_0x128c8b[_0x48efd0(0x1bd)])return _0x128c8b[_0x48efd0(0x1bd)];let _0x2831c5={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}};if(!J(_0x128c8b,_0x5b051f,_0x441d00))return _0x128c8b[_0x48efd0(0x1bd)]=_0x2831c5,_0x128c8b[_0x48efd0(0x1bd)];let _0x363e28=B(_0x128c8b),_0x2309ed=_0x363e28['elapsed'],_0x2a1744=_0x363e28['timeStamp'],_0x50681b=_0x363e28[_0x48efd0(0x1fb)],_0x3b9026={'hits':{},'ts':{}},_0x194465=Y(_0x128c8b,_0x2b48ca,_0x3b9026,_0x1b84a8,_0xebfbce),_0x4462b9=(_0x4cfe49,_0x35915b,_0x58ab0e,_0x24999c,_0x16c273,_0x2d2fae)=>{var _0x10dcd2=_0x48efd0;let _0x25b8f1=_0x128c8b[_0x10dcd2(0x1bd)];try{return _0x128c8b['_console_ninja']=_0x2831c5,_0x194465(_0x4cfe49,_0x35915b,_0x58ab0e,_0x24999c,_0x16c273,_0x2d2fae);}finally{_0x128c8b[_0x10dcd2(0x1bd)]=_0x25b8f1;}},_0x45c9b2=_0xa045db=>{_0x3b9026['ts'][_0xa045db]=_0x2a1744();},_0x5dfe65=(_0x18fdeb,_0x1126f8)=>{let _0x42fddb=_0x3b9026['ts'][_0x1126f8];if(delete _0x3b9026['ts'][_0x1126f8],_0x42fddb){let _0x998f88=_0x2309ed(_0x42fddb,_0x2a1744());_0x246d72(_0x4462b9('time',_0x18fdeb,_0x50681b(),_0x26bea9,[_0x998f88],_0x1126f8));}},_0x10aede=_0x195925=>{var _0x5c4181=_0x48efd0,_0x425e04;return _0x441d00===_0x5c4181(0x25a)&&_0x128c8b['origin']&&((_0x425e04=_0x195925==null?void 0x0:_0x195925['args'])==null?void 0x0:_0x425e04[_0x5c4181(0x1b6)])&&(_0x195925[_0x5c4181(0x23b)][0x0]['origin']=_0x128c8b[_0x5c4181(0x1a2)]),_0x195925;};_0x128c8b[_0x48efd0(0x1bd)]={'consoleLog':(_0xda0a9f,_0x9190d7)=>{var _0x257b86=_0x48efd0;_0x128c8b[_0x257b86(0x1ba)][_0x257b86(0x186)][_0x257b86(0x229)]!==_0x257b86(0x21c)&&_0x246d72(_0x4462b9('log',_0xda0a9f,_0x50681b(),_0x26bea9,_0x9190d7));},'consoleTrace':(_0x28e90d,_0x115a45)=>{var _0x3f5b82=_0x48efd0,_0x59297f,_0x582cd0;_0x128c8b['console'][_0x3f5b82(0x186)][_0x3f5b82(0x229)]!==_0x3f5b82(0x22c)&&((_0x582cd0=(_0x59297f=_0x128c8b[_0x3f5b82(0x25e)])==null?void 0x0:_0x59297f[_0x3f5b82(0x263)])!=null&&_0x582cd0['node']&&(_0x128c8b[_0x3f5b82(0x1da)]=!0x0),_0x246d72(_0x10aede(_0x4462b9(_0x3f5b82(0x255),_0x28e90d,_0x50681b(),_0x26bea9,_0x115a45))));},'consoleError':(_0x1719a2,_0x50f700)=>{var _0x18be19=_0x48efd0;_0x128c8b[_0x18be19(0x1da)]=!0x0,_0x246d72(_0x10aede(_0x4462b9(_0x18be19(0x195),_0x1719a2,_0x50681b(),_0x26bea9,_0x50f700)));},'consoleTime':_0x1b91af=>{_0x45c9b2(_0x1b91af);},'consoleTimeEnd':(_0x5e3ece,_0x45fcba)=>{_0x5dfe65(_0x45fcba,_0x5e3ece);},'autoLog':(_0x396285,_0xaf0672)=>{_0x246d72(_0x4462b9('log',_0xaf0672,_0x50681b(),_0x26bea9,[_0x396285]));},'autoLogMany':(_0xa0fc0e,_0x3df880)=>{var _0x52d754=_0x48efd0;_0x246d72(_0x4462b9(_0x52d754(0x186),_0xa0fc0e,_0x50681b(),_0x26bea9,_0x3df880));},'autoTrace':(_0x5928c5,_0x5778f0)=>{var _0x3aca45=_0x48efd0;_0x246d72(_0x10aede(_0x4462b9(_0x3aca45(0x255),_0x5778f0,_0x50681b(),_0x26bea9,[_0x5928c5])));},'autoTraceMany':(_0x214c88,_0x10d946)=>{var _0x369ebf=_0x48efd0;_0x246d72(_0x10aede(_0x4462b9(_0x369ebf(0x255),_0x214c88,_0x50681b(),_0x26bea9,_0x10d946)));},'autoTime':(_0x168d5e,_0x59e4be,_0x3bcf2b)=>{_0x45c9b2(_0x3bcf2b);},'autoTimeEnd':(_0x17c08f,_0x347576,_0x4ac253)=>{_0x5dfe65(_0x347576,_0x4ac253);},'coverage':_0x269f7a=>{var _0x4a27b2=_0x48efd0;_0x246d72({'method':_0x4a27b2(0x1a6),'version':_0x1b84a8,'args':[{'id':_0x269f7a}]});}};let _0x246d72=X(_0x128c8b,_0x130c59,_0x2d3050,_0xddf41c,_0x441d00,_0x490710,_0x5ab68a),_0x26bea9=_0x128c8b[_0x48efd0(0x24d)];return _0x128c8b['_console_ninja'];})(globalThis,_0x306708(0x16b),'55970',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.467\\\\\\\\node_modules\\\",_0x306708(0x1ef),'1.0.0','1755658057059',_0x306708(0x177),'','',_0x306708(0x1a8),_0x306708(0x1bb));\");}catch(e){}};/* istanbul ignore next */function oo_oo(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleLog(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tr(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleTrace(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_tx(/**@type{any}**/i,/**@type{any}**/...v){try{oo_cm().consoleError(i, v);}catch(e){} return v};/* istanbul ignore next */function oo_ts(/**@type{any}**/v){try{oo_cm().consoleTime(v);}catch(e){} return v;};/* istanbul ignore next */function oo_te(/**@type{any}**/v, /**@type{any}**/i){try{oo_cm().consoleTimeEnd(v, i);}catch(e){} return v;};/*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/</script>\r\n \r\n<style lang=\"less\" scoped>\r\n.wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n  .qrcode {\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    #reader {\r\n      top: 20%;\r\n      left: 0;\r\n      transform: translateY(-125px);\r\n    }\r\n\r\n    .focus-controls {\r\n      position: absolute;\r\n      bottom: 100px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      z-index: 1000;\r\n      text-align: center;\r\n\r\n      .focus-tip {\r\n        color: white;\r\n        font-size: 14px;\r\n        margin-bottom: 15px;\r\n        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);\r\n        background: rgba(0, 0, 0, 0.5);\r\n        padding: 8px 16px;\r\n        border-radius: 20px;\r\n        display: inline-block;\r\n      }\r\n\r\n      .focus-btn {\r\n        background: rgba(255, 255, 255, 0.9);\r\n        border: none;\r\n        border-radius: 50px;\r\n        padding: 12px 20px;\r\n        color: #333;\r\n        font-size: 14px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 8px;\r\n        cursor: pointer;\r\n        transition: all 0.3s ease;\r\n        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\r\n\r\n        &:hover {\r\n          background: rgba(255, 255, 255, 1);\r\n          transform: scale(1.05);\r\n        }\r\n\r\n        &:active {\r\n          transform: scale(0.95);\r\n        }\r\n\r\n        svg {\r\n          width: 18px;\r\n          height: 18px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 优化扫描框样式\r\n:deep(#reader) {\r\n  video {\r\n    border-radius: 8px;\r\n  }\r\n\r\n  // 扫描框样式优化\r\n  .qr-shaded-region {\r\n    background: rgba(0, 0, 0, 0.7) !important;\r\n  }\r\n\r\n  // 扫描框边框样式\r\n  .qr-scanner {\r\n    border: 2px solid #00ff00 !important;\r\n    border-radius: 8px !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}