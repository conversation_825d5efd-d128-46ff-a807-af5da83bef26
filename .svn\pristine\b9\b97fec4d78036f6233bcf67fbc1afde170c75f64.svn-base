import Vue from "vue";
import VueRouter, { RouteConfig, RawLocation } from "vue-router";
import store from "@/store/index";

const originalPush: any = VueRouter.prototype.push;

VueRouter.prototype.push = function push(location: RawLocation) {
  return originalPush.call(this, location).catch((err: any) => err);
};
Vue.use(VueRouter);

const routes: Array<RouteConfig> = [
  {
    path: "/",
    redirect: "/webSunriseChart",
    component: () => import(/* webpackChunkName: "home" */ "@/views/home.vue"),
    name: "Home",
    children: [
      {
        path: "/webSunriseChart",
        name: "WebSunriseChart",
        meta: { title: "web-View", path: "/webSunriseChart" },
        component: () =>
          import(
            /* webpackChunkName: "webSunriseChart" */ "@/views/supplierHomepage/index.vue"
          ),
      },
      {
        path: "/repair",
        name: "RepairModule",
        meta: { title: "返修" },
        component: () =>
          import(
            /* webpackChunkName: "repair" */
            "@/views/PublicPages/routekeep.vue"
          ),
        redirect: "/repair/repairList",
        children: [
          {
            path: "repairList",
            name: "RepairList",
            meta: { title: "返修", path: "/repair/repairList" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/repairList.vue"
              ),
          },
          {
            path: "applyRepair",
            name: "ApplyRepair",
            meta: { title: "申请返修", path: "/repair/applyRepair" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/applyRepair.vue"
              ),
          },
          {
            path: "writeApply",
            name: "WriteApply",
            meta: { title: "申请返修", path: "/repair/writeApply" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/writeApply.vue"
              ),
          },
          {
            path: "maintenanceBillStatus",
            name: "MaintenanceBillStatus",
            meta: { title: "申请状态", path: "/repair/maintenanceBillStatus" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/maintenanceBillStatus.vue"
              ),
          },
          {
            path: "equipmentDetail",
            name: "EquipmentDetail",
            meta: { title: "设备详情", path: "/repair/equipmentDetail" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/equipmentDetail.vue"
              ),
          },
          {
            path: "repairDetail",
            name: "RepairDetail",
            meta: { title: "返修详情", path: "/repair/repairDetail" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/repairDetail.vue"
              ),
          },
          {
            path: "mobileUnit",
            name: "MobileUnit",
            meta: { title: "车载设备", path: "/repair/mobileUnit" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/mobileUnit.vue"
              ),
          },
          {
            path: "unitTypeList",
            name: "UnitTypeList",
            meta: { title: "设备类型", path: "/repair/unitTypeList" },
            component: () =>
              import(
                "@/views/repairModule/applyRepair/unitTypeList.vue"
              ),
          },
          {
            path: "repairFlowList",
            name: "RepairFlowList",
            meta: { title: "返修流程", path: "/repair/repairFlowList" },
            component: () =>
              import(
                "@/views/repairModule/repairFlow/flowList.vue"
              ),
          },
          {
            path: "repairFlowStatus",
            name: "RepairFlowStatus",
            meta: { title: "返修状态", path: "/repair/repairFlowStatus" },
            component: () =>
              import(
                "@/views/repairModule/repairFlow/flowStatus.vue"
              ),
          },
          {
            path: "repairFlowContains",
            name: "RepairFlowContains",
            meta: { title: "返修信息", path: "/repair/repairFlowContains" },
            component: () =>
              import(
                "@/views/repairModule/repairFlow/flowContains.vue"
              ),
          },
          {
            path: "logisticsDetail",
            name: "LogisticsDetail",
            meta: { title: "物流详情", path: "/repair/logisticsDetail" },
            component: () =>
              import(
                "@/views/repairModule/repairFlow/logisticsDetail.vue"
              ),
          },
          {
            path: "writeEvaluate",
            name: "WriteEvaluate",
            meta: { title: "写评价", path: "/repair/writeEvaluate" },
            component: () =>
              import(
                "@/views/repairModule/evaluate/writeEvaluate.vue"
              ),
          },
          {
            path: "evaluate",
            name: "Evaluate",
            meta: { title: "我的评价", path: "/repair/evaluate" },
            component: () =>
              import(
                "@/views/repairModule/evaluate/index.vue"
              ),
          },
          {
            path: "meEvaluate",
            name: "MeEvaluate",
            meta: { title: "已评价", path: "/repair/meEvaluate" },
            component: () =>
              import(
                "@/views/repairModule/evaluate/meEvaluate.vue"
              ),
          },
          {
            path: "toBeEvaluate",
            name: "ToBeEvaluate",
            meta: { title: "待评价", path: "/repair/toBeEvaluate" },
            component: () =>
              import(
                "@/views/repairModule/evaluate/toBeEvaluate.vue"
              ),
          },
          {
            path: "addressList",
            name: "AddressList",
            meta: { title: "地址列表", path: "/repair/addressList" },
            component: () =>
              import(
                "@/views/repairModule/address/addressList.vue"
              ),
          },
          {
            path: "addAddress",
            name: "AddAddress",
            meta: { title: "添加地址", path: "/repair/addAddress" },
            component: () =>
              import(
                "@/views/repairModule/address/addAddress.vue"
              ),
          },
          {
            path: "ground",
            name: "ground",
            meta: { title: "ground", path: "/repair/ground" },
            component: () =>
              import(
                "@/views/repairModule/ground.vue"
              ),
          },
          {
            path: "roadBureau",
            name: "RoadBureau",
            meta: { title: "局段", path: "/repair/roadBureau" },
            component: () =>
              import(
                "@/views/repairModule/roadBureau.vue"
              ),
          },
          {
            path: "stations",
            name: "stations",
            meta: { title: "车站", path: "/repair/stations" },
            component: () =>
              import(
                "@/views/repairModule/stations.vue"
              ),
          },
          {
            path: "trackCircuitProducts",
            name: "stations",
            meta: { title: "轨道电路产品", path: "/repair/trackCircuitProducts" },
            component: () =>
              import(
                "@/views/repairModule/trackCircuitProducts.vue"
              ),
          },
          {
            path: "stationInfoDetail",
            name: "stationInfoDetail",
            meta: { title: "详情", path: "/repair/stationInfoDetail" },
            component: () =>
              import(
                "@/views/repairModule/stationInfoDetail.vue"
              ),
          },
          
          {
            path: "informationImport",
            name: "informationImport",
            meta: { title: "硬件配置表", path: "/repair/informationImport" },
            component: () =>
              import(
                "@/views/repairModule/informationImport.vue"
              ),
          },
          {
            path: "configurationTableDetail",
            name: "configurationTableDetail",
            meta: { title: "设备信息", path: "/repair/configurationTableDetail" },
            component: () =>
              import(
                "@/views/repairModule/configurationTableDetail.vue"
              ),
          },
          {
            path: "scanCode",
            name: "ScanCode",
            meta: { title: "二维码", path: "/repair/scanCode" },
            component: () =>
              import(
                "@/views/PublicPages/scanCode.vue"
              ),
          },
          {
            path: "managPeople",
            name: "ManagPeople",
            meta: { title: "请选择", path: "/repair/managPeople" },
            component: () =>
              import(
                "@/views/components/managPeople.vue"
              ),
          },
          {
            path: "bancard",
            name: "Bancard",
            meta: { title: "板卡", path: "/repair/bancard" },
            component: () =>
              import(
                "@/views/components/card.vue"
              ),
          },
          {
            path: "/chooseFile",
            name: "chooseFile",
            meta: { title: "上传文件", path: "/chooseFile" },
            component: () =>
              import(
                /* webpackChunkName: "chooseFile" */ "@/views/chooseFile.vue"
              ),
          },
          // {
          //   path: "mapLocation",
          //   name: "mapLocation",
          //   meta: { title: "地图选址", path: "/repair/mapLocation" },
          //   component: () =>
          //     import(
          //       "@/views/PublicPages/mapLocation.vue"
          //     ),
          // },
          {
            path: "recordBoardHome",
            name: "RecordBoardHome",
            meta: { title: "录板卡", path: "/repair/recordBoardHome" },
            component: () =>
              import(
                "@/views/repairModule/recordBoardHome.vue"
              ),
          },
          {
            path: "recordBoard",
            name: "RecordBoard",
            meta: { title: "录入板卡", path: "/repair/recordBoard" },
            component: () =>
              import(
                "@/views/repairModule/recordBoard.vue"
              ),
          },
        ]
      },
      {
        path: "/videoCall",
        name: "VideoCall",
        meta: { title: "客服登陆", path: "/videoCall" },
        component: () =>
          import(
            /* webpackChunkName: "videoCall" */ "@/views/videoCall.vue"
          ),
      },

      {
        path: "/submitForm",
        name: "submitForm",
        meta: { title: "施工确认", path: "/submitForm" },
        component: () =>
          import(
            /* webpackChunkName: "construction" */ "@/views/wx_construction/submitForm.vue"
          ),
      },
      {
        path: "/reviewMilestone",
        name: "ReviewMilestone",
        meta: { title: "评审里程碑", path: "/reviewMilestone" },
        component: () =>
          import(
            /* webpackChunkName: "plim" */ "@/views/web/srProjectMgt/reviewMilestone.vue"
          ),
      },
      {
        path: "/confirmMilestone",
        name: "ConfirmMilestone",
        meta: { title: "确认里程碑", path: "/confirmMilestone" },
        component: () =>
          import(
            /* webpackChunkName: "plim" */ "@/views/web/srProjectMgt/confirmMilestone.vue"
          ),
      },
      {
        path: "/researchResult",
        name: "ResearchResult",
        meta: { title: "里程碑状态", path: "/researchResult" },
        component: () =>
          import(
            /* webpackChunkName: "plim" */ "@/views/web/srProjectMgt/researchResult.vue"
          ),
      },
    ]
  },
  {
    path: "/Ddinglogin",
    name: "Ddinglogin",
    meta: { title: "登录", path: "/Ddinglogin" },
    component: () =>
      import(
        /* webpackChunkName: "Ddinglogin" */ "@/views/login/Ddinglogin.vue"
      ),
  },
  {
    path: "/DdingCoffee",
    redirect: "/DdingCoffee/login",
    component: () => import(/* webpackChunkName: "home" */ "@/views/home.vue"),
    name: "DdingCoffee",
    children: [
      //咖啡登录页
      {
        path: "/DdingCoffee/login",
        name: "login",
        meta: { title: "记账码", path: "/DdingCoffee/login" },
        component: () =>
          import(
        /* webpackChunkName: "coffee" */ "@/views/coffee/CoffeeLogin.vue"
          ),
      },
      //咖啡列首页
      {
        path: "/DdingCoffee/home",
        name: "home",
        meta: { title: "记账码", path: "/DdingCoffee/home" },
        component: () =>
          import(
            /* webpackChunkName: "coffee" */ "@/views/coffee/CoffeeHome.vue"
          ),
      },
      //咖啡列表页
      {
        path: "/DdingCoffee/list",
        name: "账单",
        meta: { title: "账单", path: "/DdingCoffee/list" },
        component: () =>
          import(
            /* webpackChunkName: "coffee" */ "@/views/coffee/CoffeeList.vue"
          ),
      },
      //咖啡黑名单页
      {
        path: "/DdingCoffee/blackList",
        name: "黑名单",
        meta: { title: "黑名单", path: "/DdingCoffee/blackList" },
        component: () =>
          import(
            /* webpackChunkName: "coffee" */ "@/views/coffee/blackList.vue"
          ),
      },
      //扫码页
      {
        path: "/DdingCoffee/scanview",
        name: "scanview",
        meta: { title: "扫码", path: "/DdingCoffee/scanview" },
        component: () =>
          import(
            /* webpackChunkName: "coffee" */ "@/views/coffee/scanview.vue"
          ),
      },
    ]
  },
  {
    path: "/IRead",
    redirect: "/IRead/login",
    component: () => import(/* webpackChunkName: "home" */ "@/views/home.vue"),
    name: "IRead",
    children: [
      {
        path: "/IRead/login",
        name: "login",
        meta: { title: "IRead", path: "/IRead/login" },
        component: () =>
          import(
        /* webpackChunkName: "iread" */ "@/views/IRead/login.vue"
          ),
      },
      {
        path: "/IRead/home",
        name: "home",
        meta: { title: "IRead", path: "/IRead/home" },
        component: () =>
          import(
        /* webpackChunkName: "iread" */ "@/views/IRead/home.vue"
          ),
        redirect: "/IRead/index",
        children: [
          {
            path: "/IRead/index",
            meta: { title: "IRead", path: "/IRead/index" },
            component: () =>
              import(
                "@/views/IRead/index.vue"
              ),
          },
          {
            path: "/IRead/scanview",
            name: "scanview",
            meta: { title: "IRead", path: "/IRead/scanview" },
            component: () =>
              import(
                "@/views/IRead/scanview.vue"
              ),
          },
          {
            path: "/IRead/mine",
            name: "mine",
            meta: { title: "IRead", path: "/IRead/mine" },
            component: () =>
              import(
                "@/views/IRead/mine.vue"
              ),
          }
        ]
      },
      {
        path: "/IRead/bookInfo",
        name: "bookInfo",
        meta: { title: "bookInfo", path: "/IRead/bookInfo" },
        component: () =>
          import(
        /* webpackChunkName: "iread" */ "@/views/IRead/bookInfo.vue"
          ),
      },
      {
        path: "/IRead/more",
        name: "more",
        meta: { title: "IRead", path: "/IRead/more" },
        component: () =>
          import(
        /* webpackChunkName: "iread" */ "@/views/IRead/more.vue"
          ),
      },
      {
        path: "/IRead/search",
        name: "search",
        meta: { title: "search", path: "/IRead/search" },
        component: () =>
          import(
        /* webpackChunkName: "iread" */ "@/views/IRead/search.vue"
          ),
      },
    ]
  },
  //手机端空白签署
  {
    path: "/blankSign",
    name: "blankSign",
    meta: { title: "blankSign", path: "/blankSign" },
    component: () =>
      import(
        /* webpackChunkName: "webSunriseChart" */ "@/views/sign/blankSign.vue"
      ),
  },
  //签署列表页
  {
    path: "/signList",
    name: "signList",
    meta: { title: "signList", path: "/signList" },
    component: () =>
      import(
        /* webpackChunkName: "webSunriseChart" */ "@/views/sign/signList.vue"
      ),
  },
  //财务办结同步错误页
  {
    path: "/financeErrMsg",
    name: "financeErrMsg",
    meta: { title: "financeErrMsg", path: "/financeErrMsg" },
    component: () =>
      import(
        /* webpackChunkName: "webViewPage" */ "@/views/finance/financeErrMsg.vue"
      ),
  },
  // ======================================== 钉钉督办事项 ======================================== 
  // 钉钉督办事项 登录页
  {
    path: "/superviseMatterLogin",
    name: "superviseMatterLogin",
    meta: { title: "superviseMatterLogin", path: "/superviseMatterLogin" },
    component: () =>
      import(
      /* webpackChunkName: "superviseMatter" */ "@/views/superviseMatter/login.vue"
      ),
  },
  {
    path: "/superviseMatter",
    name: "superviseMatter",
    meta: { title: "督办事项", },
    component: () =>
      import(/* webpackChunkName: "superviseMatter" */ "@/views/superviseMatter/index.vue"),
    children: [
      {
        path: "list",
        name: "list",
        meta: { title: "督办事项台账", path: "/superviseMatter/list" },
        component: () =>
          import(
            "@/views/superviseMatter/list.vue"
          ),
      },
      {
        path: "proMeetDetails",
        name: "proMeetDetails",
        meta: { title: "生产交班会明细", path: "/superviseMatter/proMeetDetails" },
        component: () =>
          import(
            "@/views/superviseMatter/details/proMeetDetails.vue"
          ),
      },
      {
        path: "proMeetEditDetails",
        name: "proMeetEditDetails",
        meta: { title: "生产交班会明细", path: "/superviseMatter/proMeetEditDetails" },
        component: () =>
          import(
            "@/views/superviseMatter/details/admMeetEdit.vue"
          ),
      },
      {
        path: "admMeetDetails",
        name: "admMeetDetails",
        meta: { title: "行政交班会明细", path: "/superviseMatter/admMeetDetails" },
        component: () =>
          import(
            "@/views/superviseMatter/details/admMeetDetails.vue"
          ),
      },
      {
        path: "jyMeetDetails",
        name: "jyMeetDetails",
        meta: { title: "生成影响经营详情", path: "/superviseMatter/jyMeetDetails" },
        component: () =>
          import(
            "@/views/superviseMatter/details/jyMeetDetails.vue"
          ),
      },
      {
        path: "returnVisitDetails",
        name: "returnVisitDetails",
        meta: { title: "回访问题追踪", path: "/superviseMatter/returnVisitDetails" },
        component: () =>
          import(
            "@/views/superviseMatter/details/returnVisitDetails.vue"
          ),
      },
    ],
  },
  {
    path:"/ZTdevicelist",
    name: "ZTdevicelist",
    meta: { title: "站套设备清单", path: "/ZTdevicelist" },
    component: () =>
      import(
      /* webpackChunkName: "superviseMatter" */ "@/views/ZTdevicelist/index.vue"
      ),
  },
  {
    path:"/completed",
    name: "completed",
    meta: { title: "站套设备清单", path: "/completed" },
    component: () =>
      import(
      /* webpackChunkName: "superviseMatter" */ "@/views/ZTdevicelist/completed.vue"
      ),
  },
  {
    path:"/issuesKey",
    name: "issuesKey",
    meta: { title: "重点问题详情", path: "/issuesKey" },
    component: () =>
      import(
      /* webpackChunkName: "superviseMatter" */ "@/views/issuesKey/index.vue"
      ),
  },
  {
    path: "/privacyAgreement",
    name: "PrivacyAgreement",
    meta: { title: "privacyAgreement", path: "/privacyAgreement" },
    component: () =>
      import(
      /* webpackChunkName: "superviseMatter" */ "@/views/privacyAgreement.vue"
      ),
  },
  // 商机
  {
    path: "/crm",
    name: "crm",
    meta: { title: "商机", },
    component: () =>
      import(/* webpackChunkName: "crm" */ "@/views/shangJi/index.vue"),
    children: [
      {
        path: "login",
        name: "login",
        meta: { title: "商机", path: "/crm/login" },
        component: () =>
          import(
            "@/views/shangJi/login.vue"
          ),
      },
      {
        path: "List",
        name: "List",
        meta: { title: "商机", path: "/crm/List" },
        component: () =>
          import(
            "@/views/shangJi/List.vue"
          ),
      },
      {
        path: "details",
        name: "details",
        meta: { title: "商机详情", path: "/crm/details" },
        component: () =>
          import(
            "@/views/shangJi/details.vue"
          ),
      },
      {
        path: "readOnlyDetails",
        name: "readOnlyDetails",
        meta: { title: "商机详情", path: "/crm/readOnlyDetails" },
        component: () =>
          import(
            "@/views/shangJi/readOnlyDetails.vue"
          ),
      },
    ],
  },
  // 确认项目要求到货时间
  {
    path: "/singleProduct",
    name: "singleProduct",
    meta: { title: "单产品", },
    component: () =>
      import(/* webpackChunkName: "singleProduct" */ "@/views/singleProduct/index.vue"),
    children: [
      {
        path: "login",
        name: "login",
        meta: { title: "单产品", path: "/singleProduct/login" },
        component: () =>
          import(
            "@/views/singleProduct/login.vue"
          ),
      },
      {
        path: "List",
        name: "List",
        meta: { title: "单产品", path: "/singleProduct/List" },
        component: () =>
          import(
            "@/views/singleProduct/list.vue"
          ),
      },
      {
        path: "mobileView",
        name: "mobileView",
        meta: { title: "单产品", path: "/singleProduct/mobileView" },
        component: () =>
          import(
            "@/views/singleProduct/mobileView.vue"
          ),
      },
      {
        path: "UpdateMsgIn",
        name: "UpdateMsgIn",
        meta: { title: "单产品", path: "/singleProduct/UpdateMsgIn" },
        component: () =>
          import(
            "@/views/singleProduct/UpdateMsgIn.vue"
          ),
      },
    ],
  },
];

const router = new VueRouter({
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  }
});


export default router;
