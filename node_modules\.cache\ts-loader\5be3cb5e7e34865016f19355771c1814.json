{"remainingRequest": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js!E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js!E:\\smallProgram\\node_modules\\ts-loader\\index.js??ref--13-3!E:\\smallProgram\\src\\router\\index.ts", "dependencies": [{"path": "E:\\smallProgram\\src\\router\\index.ts", "mtime": 1755670430119}, {"path": "E:\\smallProgram\\babel.config.js", "mtime": 1753929329699}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751509197733}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\ts-loader\\index.js", "mtime": 1751509198841}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}