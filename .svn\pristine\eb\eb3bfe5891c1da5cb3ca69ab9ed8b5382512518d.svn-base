import { request } from "@/utils";

class sign {
  //签署文档 - 查看我的文件
  public signDocument(payload: any) {
    return request({
      url: "/productsign/signDocument",
      method: "post",
      params: payload,
    });
  }
  //签署文档 - 签署链接
  public dosignFeignDingTalk(payload: any) {
    return request({
      url: "/productsign/dosignFeignDingTalk",
      method: "post",
      params: payload,
    });
  }
}

const signService = new sign();
export { signService };