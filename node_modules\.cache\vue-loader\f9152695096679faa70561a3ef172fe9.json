{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\return\\returnWeb.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\return\\returnWeb.vue", "mtime": 1755573917690}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}