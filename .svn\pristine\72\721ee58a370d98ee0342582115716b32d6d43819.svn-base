<template>
  <div class="box_list">
   <div  class="uploadLoadingBox"></div>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from "vuex";
import { tokenGuard, toolGuard } from "@/utils";
export default {
  name: "blankSign",
  data() {
    return {
    }
  },
    watch: {
    $route: {
      async handler(val, oldval) {
        console.log(val.query, "watch-$router");
        this.tokenData = tokenGuard.getToken();
        console.log('tokenData',this.tokenData);
        let ua = navigator.userAgent.toLowerCase();
      console.log("ua", ua);
        if (val.query ) {
          if (!tokenGuard.getToken()) {
            this.NoPermission();
          }
        }
        if (this.tokenData) {
          this.goSign()
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    
  },
  methods: {
    ...mapActions("sign_modules/sign", [
            "dosignFeignDingTalk", //查看我的文件
        ]),
     NoPermission() {
      console.log("NoPermission");
      //判断浏览器的类型
      let ua = navigator.userAgent.toLowerCase();
      console.log("ua", ua);
      
      if (String(ua).includes("dingtalk")) {
        //钉钉
        this.ddGetAuthCode({
          corpId: "dingc6ea79f3e805963a4ac5d6980864d335", //通号
          success: (res) => {
            this.init_Permission(res.code);
          },
          fail: () => {
            console.log("dd失败");
          },
          complete: () => {
            console.log("dd完成");
          },
        });
      }else{
          this.goSign()
      }
    },
    async init_Permission(code) {
      const res = await this.dingUserInfoDetailLogin({
        code: code,
      });
      if (res && res.code == 0) {
        // --------------------------------------------------------------
        // 清除老数据
        localStorage.removeItem("UnInfoFlag");
        tokenGuard.removeMsgOnceTime();
        await tokenGuard.setToken(res.msg);
        localStorage.setItem("userInfo", JSON.stringify(res.data));
        // --------------------------------------------------------------
         this.goSign()
      } else {
        this.MessageDaiban = true;
        return;
      }
    },
    async goSign(){
        const res=await this.dosignFeignDingTalk({
          fileId:this.$route.query.fileId,
          userId:this.$route.query.userId
        })
        if(res.code==0){
            window.open(res.data)
            
        }else{
            this.$toast(res.msg)
        }
    },
  }
}
</script>

<style scoped lang="less">
.box_list{
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    
}
.uploadLoadingBox {
  background: #ffffff url(../../assets/images/customerProperty/0303.gif)
    no-repeat;
  background-size: 150px auto;
  min-height: 30vh;
  width: 100%;
  background-position: center;
}
</style>
