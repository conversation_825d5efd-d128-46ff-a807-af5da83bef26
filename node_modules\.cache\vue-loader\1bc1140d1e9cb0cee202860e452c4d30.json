{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\sign\\blankSign.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\sign\\blankSign.vue", "mtime": 1755504836657}, {"path": "E:\\smallProgram\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751509198730}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}