<template>
  <div class="wrapper">
    <!-- <MyHeader :name="'调用摄像头扫码'" left="arrow-left" @goBackEv="$emit('goBack')" /> -->
    <div class="qrcode">
      <div id="reader"></div>
      <!-- 添加对焦提示和手动对焦按钮 -->
      <div class="focus-controls">
        <div class="focus-tip">对准二维码，轻触屏幕对焦</div>
        <button class="focus-btn" @click="requestFocus" v-if="isScanning">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="3"/>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
          </svg>
          对焦
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { Html5Qrcode, Html5QrcodeScanType } from "html5-qrcode";
export default {
  //   components: { QrcodeStream },
  data() {
    return {
      html5QrCode: null,
      isScanning: false,
      cameraId: null,
    };
  },
  created() {
    this.getCameras();
  },
  beforeDestroy() {
    if (this.html5QrCode) this.stop();
  },
  methods: {
    getCameras() {
      Html5Qrcode.getCameras()
        .then((devices) => {
          if (devices && devices.length) {
            // 优先选择后置摄像头，通常具有更好的自动对焦功能
            const backCamera = devices.find(device =>
              device.label.toLowerCase().includes('back') ||
              device.label.toLowerCase().includes('rear') ||
              device.label.toLowerCase().includes('environment')
            );
            this.cameraId = backCamera ? backCamera.id : devices[devices.length - 1].id;
            this.html5QrCode = new Html5Qrcode("reader");
            this.start();
          }
        })
        .catch((err) => {
          console.error("获取摄像头失败:", err);
          this.html5QrCode = new Html5Qrcode("reader");
          this.$toast("您需要授予相机访问权限");
        });
    },
    start() {
      if (this.isScanning) return;

      // 使用选定的摄像头ID或回退到facingMode
      const cameraConfig = this.cameraId ? this.cameraId : { facingMode: "environment" };

      this.html5QrCode
        .start(
          cameraConfig,
          {
            fps: 10, // 提高帧率以获得更好的扫描体验
            qrbox: { width: 280, height: 280 }, // 启用扫描框UI，有助于对焦
            aspectRatio: 1.0, // 1:1比例更适合二维码扫描
            // 添加更多配置选项以优化小码扫描
            videoConstraints: {
              width: { min: 640, ideal: 1280, max: 1920 },
              height: { min: 480, ideal: 720, max: 1080 },
              // 启用自动对焦相关设置
              focusMode: "continuous",
              // 支持缩放以便扫描小码
              zoom: true,
              // 添加更多约束以获得更清晰的图像
              facingMode: "environment"
            },
            // 支持多种码类型，包括小的条形码
            supportedScanTypes: [
              Html5QrcodeScanType.SCAN_TYPE_CAMERA
            ]
          },
          (decodedText, decodedResult) => {
            // do something when code is read
            console.log("decodedText", decodedText);
            console.log("decodedResult", decodedResult);

            // 扫描成功后立即停止扫描
            this.isScanning = false;
            this.stop();

            // 保存结果并返回
            localStorage.setItem('csqrcode', JSON.stringify(decodedResult));
            this.$router.go(-1);
          }
        )
        .then(() => {
          this.isScanning = true;
          console.log("QR Code scanning started successfully.");
        })
        .catch((err) => {
          this.isScanning = false;
          console.log("扫码错误信息", err);

          // 如果是约束错误，尝试使用更基本的配置重新启动
          if (err.name === "OverconstrainedError") {
            console.log("尝试使用基本配置重新启动...");
            this.startWithBasicConfig();
            return;
          }

          // 错误信息处理
          if (typeof err == "string") {
            this.$toast(err);
          } else {
            if (err.name == "NotAllowedError")
              return this.$toast("您需要授予相机访问权限");
            if (err.name == "NotFoundError")
              return this.$toast("这个设备上没有摄像头");
            if (err.name == "NotSupportedError")
              return this.$toast(
                "摄像头访问只支持在安全的上下文中，如https或localhost"
              );
            if (err.name == "NotReadableError")
              return this.$toast("相机被占用");
            if (err.name == "OverconstrainedError")
              return this.$toast("摄像头配置不支持，已尝试基本配置");
            if (err.name == "StreamApiNotSupportedError")
              return this.$toast("此浏览器不支持流API");
          }
        });
    },
    // 基本配置的备用启动方法
    startWithBasicConfig() {
      if (this.isScanning) return;

      const cameraConfig = this.cameraId ? this.cameraId : { facingMode: "environment" };

      this.html5QrCode
        .start(
          cameraConfig,
          {
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0
          },
          (decodedText, decodedResult) => {
            this.isScanning = false;
            this.stop();
            localStorage.setItem('csqrcode', JSON.stringify(decodedResult));
            this.$router.go(-1);
          }
        )
        .then(() => {
          this.isScanning = true;
          console.log("QR Code scanning started with basic config.");
        })
        .catch((err) => {
          this.isScanning = false;
          console.error("基本配置启动也失败:", err);
          this.$toast("摄像头启动失败，请检查设备权限");
        });
    },

    stop() {
      if (!this.html5QrCode || !this.isScanning) return;

      this.html5QrCode
        .stop()
        .then(() => {
          this.isScanning = false;
          console.log("QR Code scanning stopped.");
        })
        .catch((err) => {
          console.error("停止扫描失败:", err);
          this.isScanning = false;
        });
    },

    // 手动对焦方法
    async requestFocus() {
      try {
        // 获取视频流
        const videoElement = document.querySelector('#reader video');
        if (videoElement && videoElement.srcObject) {
          const stream = videoElement.srcObject;
          const videoTrack = stream.getVideoTracks()[0];

          // 检查是否支持对焦功能
          const capabilities = videoTrack.getCapabilities();
          if (capabilities.focusMode && capabilities.focusMode.includes('manual')) {
            // 尝试手动对焦
            await videoTrack.applyConstraints({
              advanced: [{ focusMode: 'manual', focusDistance: 0.1 }]
            });

            // 短暂延迟后切换回连续对焦
            setTimeout(async () => {
              try {
                await videoTrack.applyConstraints({
                  advanced: [{ focusMode: 'continuous' }]
                });
              } catch (e) {
                console.log('切换回连续对焦失败:', e);
              }
            }, 1000);

            this.$toast('正在对焦...');
          } else {
            this.$toast('设备不支持手动对焦');
          }
        }
      } catch (error) {
        console.error('对焦失败:', error);
        this.$toast('对焦失败，请重新扫描');
      }
    },
  },
};
</script>
 
<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  .qrcode {
    position: relative;
    height: 100%;
    width: 100%;

    #reader {
      top: 20%;
      left: 0;
      transform: translateY(-125px);
    }

    .focus-controls {
      position: absolute;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      text-align: center;

      .focus-tip {
        color: white;
        font-size: 14px;
        margin-bottom: 15px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
        background: rgba(0, 0, 0, 0.5);
        padding: 8px 16px;
        border-radius: 20px;
        display: inline-block;
      }

      .focus-btn {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 50px;
        padding: 12px 20px;
        color: #333;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }

        svg {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

// 优化扫描框样式
:deep(#reader) {
  video {
    border-radius: 8px;
  }

  // 扫描框样式优化
  .qr-shaded-region {
    background: rgba(0, 0, 0, 0.7) !important;
  }

  // 扫描框边框样式
  .qr-scanner {
    border: 2px solid #00ff00 !important;
    border-radius: 8px !important;
  }
}
</style>
