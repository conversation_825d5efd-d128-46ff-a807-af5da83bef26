{"remainingRequest": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\smallProgram\\src\\views\\repairModule\\trackCircuitProducts.vue?vue&type=style&index=0&id=7e5b1624&lang=less&scoped=true", "dependencies": [{"path": "E:\\smallProgram\\src\\views\\repairModule\\trackCircuitProducts.vue", "mtime": 1755670347076}, {"path": "E:\\smallProgram\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751509198155}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751509199665}, {"path": "E:\\smallProgram\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751509198719}, {"path": "E:\\smallProgram\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1751509198495}, {"path": "E:\\smallProgram\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751509197765}, {"path": "E:\\smallProgram\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751509199172}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYm94ew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDB2aDsNCiAgYmFja2dyb3VuZDogI2YwZjhmZjsNCiAgLy8gcGFkZGluZzogOHB4IDEwcHggMHB4Ow0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICBvdmVyZmxvdzogYXV0bzsNCi8vICAgZGlzcGxheTogZmxleDsNCi8vICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCn0NCi5oZWFkZXJ7DQogICAgd2lkdGg6IDEwMCU7DQogICAgcGFkZGluZzogMTBweCAyMHB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogICAgZGlzcGxheTogZmxleDsNCiAgICANCn0NCi5oZWFkZXItdGl0bGUgew0KICBwYWRkaW5nOiA1cHggMTBweDsNCiAgbWFyZ2luOiA1cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzOw0KICBib3JkZXI6IDFweCBzb2xpZCAjMDAwOw0KDQp9DQoNCi5oZWFkZXItdGl0bGUuYWN0aXZlIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE5ODlmYTsNCiAgY29sb3I6ICNmZmY7DQogIGJvcmRlcjogbm9uZTsNCn0NCi5jb250ZW50ew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIHBhZGRpbmc6IDEwcHg7DQogICAgbWFyZ2luLXRvcDoxMHB4IDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KfQ0KLmNvbnRlbnQtYm94ew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGRpc3BsYXk6IGZsZXg7DQp9DQouY29udGVudC1sZWZ0ew0KICAgIHdpZHRoOiAxNSU7DQogICAgZGl2ew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgaGVpZ2h0OiA2MHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogNjBweDsNCiAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIGNvbG9yOiAjMDE1YTlkOw0KICAgICAgICBmb250LWZhbWlseTog6buR5L2TOw0KICAgICAgICBmb250LXdlaWdodDogNzAwOw0KICAgIH0NCn0NCi5jb250ZW50LWxpc3R7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWV2ZW5seTsNCiAgICAuaXRlbXsNCiAgICAgICAgd2lkdGg6IDE5JTsNCiAgICAgICAgLml0ZW0tdGl0bGV7DQogICAgICAgICAgICBjb2xvcjogIzU3NTc1NzsNCiAgICAgICAgICAgIGhlaWdodDogNjBweDsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiA2MHB4Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgZm9udC1mYW1pbHk6IOm7keS9kzsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICAgIH0NCiAgICAgICAgLml0ZW0taXNVc2V7DQogICAgICAgICAgICBoZWlnaHQ6IDYwcHg7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogNjBweDsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgIC5zdWNjZXNzLWljb257DQogICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICAgIGNvbG9yOiAjMDdjMTYwOw0KICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgICAgICAgICBkaXZ7DQogICAgICAgICAgICAgICAgICAgIHdpZHRoOiAyMHB4Ow0KICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDIwcHg7DQogICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgICAgICAgIGJvcmRlcjogMXB4IGRhc2hlZCAjMDdjMTYwDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgLmVycm9yLWljb257DQogICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICAgIGNvbG9yOiAjN2M3NTc1Ow0KICAgICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICAgICAgICAgIGRpdnsNCiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDE1cHg7DQogICAgICAgICAgICAgICAgICAgIGhlaWdodDogMTVweDsNCiAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgICAgICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAxcHggZGFzaGVkICNhM2EzYTMNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgIH0NCiAgICB9DQp9DQovLyBQb3B1cCDlvLnlh7rlsYLmoLflvI8NCi5wb3B1cC1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBoZWlnaHQ6IDEwMCU7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KfQ0KDQoucG9wdXAtaGVhZGVyIHsNCiAgcGFkZGluZzogMjBweCAyMHB4IDAgMjBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlZWU7DQoNCiAgLnBvcHVwLXRpdGxlIHsNCiAgICBmb250LXNpemU6IDE1cHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBjb2xvcjogIzMzMzsNCiAgICBtYXJnaW46IDA7DQogICAgcGFkZGluZy1ib3R0b206IDE2cHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQogIA0KfQ0KLnBvcHVwLXNlY3Rpb25OYW1lew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogNjBweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIHBhZGRpbmc6IDIwcHggMTVweDsNCiAgICBmb250LXdlaWdodDogNjAwOw0KICAgICBjb2xvcjogIzAxNWE5ZDs7DQogIH0NCiAgLnBvcHVwLWZvcm17DQogICAgd2lkdGg6IDEwMCU7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAucG9wdXAtZm9ybS1pdGVtew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgcGFkZGluZzogMTBweDsNCg0KICAgICAgICAucG9wdXAtZm9ybS1pdGVtLXRpdGxlew0KICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICAgIC50aXRsZXsNCiAgICAgICAgICAgICAgICBjb2xvcjogIzAxNWE5ZDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC5udW1iZXJ7DQogICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgICAgICAgICAgICAgd2lkdGg6IDYwJTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgIH0NCiAgfQ0KLmxhYmVsLXRleHR7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICBjb2xvcjogIzAxNWE5ZDs7DQp9DQouY29udGVudC1tb25pdG9yeyANCiAgICB3aWR0aDogMTAwJTsNCiAgICAuY29udGVudC1tb25pdG9yLXRpdGxlew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgcGFkZGluZzogMTBweDsNCiAgICB9DQogICAgLmNvbnRlbnQtbW9uaXRvci1pdGVtew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZmxleC13cmFwOiB3cmFwOw0KICAgICAgICAvLyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWV2ZW5seTsNCiAgICAgICAgLm1vbmlvci1pdGVtew0KICAgICAgICAgICAgd2lkdGg6IDIyJTsNCiAgICAgICAgICAgIGhlaWdodDogNjBweDsNCiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICBtYXJnaW46IDEwcHggMDsNCiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgICBtYXJnaW46IDAgNXB4Ow0KICAgICAgICAgICAgLnRpdGxlew0KICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgICB9DQogICAgICAgICAgICAuaXN1c2VjaGFuZ2V7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgICAgICAgICAgIHdpZHRoOiAyMHB4Ow0KICAgICAgICAgICAgICAgIGhlaWdodDogMjBweDsNCiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgYm9yZGVyOiAxcHggZGFzaGVkICNhM2EzYTMNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC5pc3VzZXBhc3N7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgICAgICAgICAgIHdpZHRoOiAyMHB4Ow0KICAgICAgICAgICAgICAgIGhlaWdodDogMjBweDsNCiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgICAgICAgICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICMwN2MxNjA7DQogICAgICAgICAgICAgICAgYm9yZGVyOiAxcHggZGFzaGVkICMwN2MxNjANCiAgICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["trackCircuitProducts.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "trackCircuitProducts.vue", "sourceRoot": "src/views/repairModule", "sourcesContent": ["<template>\r\n  <div class='box'>\r\n    <!-- <van-nav-bar\r\n      :title='$route.query.categoryName'\r\n      left-arrow\r\n      @click-left=\"onClickLeft\"\r\n    /> -->\r\n\r\n    <div class=\"header\">\r\n      <div \r\n        class=\"header-title\" \r\n        v-for=\"(item,index) in trackData.children\" \r\n        :key=\"index\"\r\n        :class=\"{ active: selectedIndex === index }\"\r\n        @click=\"selectItem(index,item)\"\r\n      >\r\n        {{ item.trackCircuitConfig.drawingName }}\r\n      </div>\r\n    </div>\r\n    <!--移动柜/电码化发送柜  -->\r\n    <div class=\"content\" v-if=\"$route.query.type==1\">\r\n        <div class=\"content-box\">\r\n            <div class=\"content-left\">\r\n                <div></div>\r\n                <div class=\"content-title\" v-for=\"item in titleList\">\r\n                {{ item.configName}}:\r\n                </div>\r\n                <div></div>\r\n                <div class=\"content-title\" v-for=\"item in titleList\">\r\n                {{ item.configName}}:\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"content-list\">\r\n                <div class=\"item\" v-for=\"item in sectionInfoList\">\r\n                    <div class=\"item-title\">\r\n                        {{ item.sectionName}}\r\n                    </div>\r\n                    <div class=\"item-isUse\" v-for=\"value in item.configInfoList\" @click=\"checkTypeFun(item,value)\">\r\n                        <div class=\"success-icon\"  v-if=\"value.isUse\">\r\n                            <div ><van-icon name=\"success\" /></div>\r\n                            已录</div>\r\n                        <div class=\"error-icon\" v-else><div ></div>未录</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!-- 模拟网络接口柜 -->\r\n    <div class=\"content\" v-if=\"$route.query.type==2\">\r\n        <div class=\"content-monitor\" v-for=\"item in sectionInfoList\" :key=\"item.sectionId\">\r\n            <div class=\"content-monitor-title\">\r\n                {{ item.sectionName }}\r\n            </div>\r\n            <div class=\"content-monitor-item\" >\r\n                <div class=\"monior-item\" v-for=\"item1 in item.configInfoList\r\n\" :key=\"item1.id\">\r\n                    <div class=\"title\">{{ item1.configName }}</div>\r\n                    <div :class=\"item1.isUse?'isusepass':'isusechange'\" @click=\"checkTypeFun(item,item1)\">\r\n                        <van-icon name=\"success\" v-if=\"item1.isUse\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!-- 已录弹出层 -->\r\n     <van-popup v-model=\"isusePopup\" @close=\"closePopup\" position=\"bottom\" :style=\"{ height: '50%' }\" >\r\n         <div class=\"popup-container\">\r\n            <div class=\"popup-header\">\r\n                <h3 class=\"popup-title\">{{ popuptitle }}</h3>\r\n            </div>\r\n            <div class=\"popup-sectionName\">\r\n                {{ unhireitem.configName }}\r\n            </div>\r\n            <div class=\"popup-sectionName\" v-if=\"$route.query.type==2\">\r\n                {{ unhireitem.sectionName }}\r\n            </div>\r\n            <div class=\"popup-form\">\r\n                <div class=\"popup-form-item\" style=\"display: flex;align-items: center;font-size: 14px;\">\r\n                    <!-- <van-field\r\n                        v-model=\"unhireitem.number\"\r\n                        center\r\n                        clearable\r\n                        readonly\r\n                        label-width=\"7rem\"\r\n                        :type=\"unhireitem.number&&unhireitem.number.length>=30?'textarea':''\"\r\n                        >\r\n                        <template #label>\r\n                           <div class=\"label-text\">\r\n                            序列号：\r\n                           </div> \r\n                        </template>\r\n                        <template #button>\r\n                            <van-button size=\"small\" type=\"info\" @click=\"unbind\">{{unhireitem.isUse?'解绑':'绑定'}}</van-button>\r\n                        </template>\r\n                        </van-field> -->\r\n                        <!-- <div class=\"popup-form-item-title\">\r\n                            <div class=\"title\">序列号：</div>\r\n                            <div class=\"number\">{{ unhireitem.number }}</div>\r\n                            <div class=\"btn\">\r\n                                 <van-button size=\"small\" type=\"info\" @click=\"unbind\">{{unhireitem.isUse?'解绑':'绑定'}}</van-button>\r\n                            </div>\r\n                        </div> -->\r\n                           <div class=\"title\" style=\"width: 8rem;font-size: 14px;color: #015a9d;\">序列号：</div>\r\n                            <div class=\"serial-number-display\" style=\"flex: 1; padding: 0 10px;  white-space: wrap;\">\r\n                                {{ unhireitem.number }}\r\n                            </div>\r\n                            <van-button style=\"width: 65px;\" size=\"small\" type=\"info\" @click=\"unbind\">\r\n                                {{unhireitem.isUse?'解绑':'绑定'}}\r\n                            </van-button>\r\n                </div>\r\n                 <div class=\"popup-form-item\">\r\n                     <van-field\r\n                        v-model=\"unhireitem.version\"\r\n                        center\r\n                        clearable\r\n                        label-width=\"7rem\"\r\n                        type=\"number\"\r\n                        :readonly=\"unhireitem.isUse\"\r\n                       \r\n                        >\r\n                          <!-- @click=\"versionPopup = true\" -->\r\n                        <template #label>\r\n                           <div class=\"label-text\">\r\n                            版本号：\r\n                           </div> \r\n                        </template>\r\n                        </van-field>\r\n                 </div>\r\n            </div>\r\n        </div>\r\n     </van-popup>\r\n     <!-- 版本弹出层 -->\r\n    <van-popup v-model=\"versionPopup\"  round\r\n      :style=\"{ height: '60%' }\" @close=\"closeversionPopup\" :default-index=\"0\" position=\"bottom\" >\r\n            <van-picker\r\n            title=\"版本选择\"  \r\n            show-toolbar\r\n            :columns=\"columns\"\r\n            @confirm=\"onConfirm\"\r\n            @cancel=\"onCancel\"\r\n            />\r\n    </van-popup>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport wx from \"weixin-js-sdk\";\r\nimport { mapState, mapMutations, mapGetters, mapActions } from \"vuex\";\r\nexport default {\r\n  components: {},\r\n  props: {},\r\n  data() {\r\n    return {\r\n      trackData:[],\r\n      titleList:[],\r\n      sectionInfoList:[],\r\n      selectedIndex: 0, // 记录当前选中的项\r\n      selectedItem: {}, // 记录当前选中的项\r\n      isusePopup:false,\r\n      popuptitle:'',\r\n      unhireitem:{},\r\n      versionPopup:false,\r\n    //   columns:['1.0','1.1','1.2'],\r\n      columns:[\r\n        {text:'1.0',value:'1.0'},\r\n        {text:'1.1',value:'1.1'},\r\n        {text:'1.2',value:'1.2'},\r\n      ],\r\n      isWeChat :false,\r\n\r\n    };\r\n  },\r\n  computed: {\r\n     ...mapGetters([\"scanCodeInfo\"]),\r\n  },\r\n  watch: {},\r\n  //方法集合\r\n  methods: {\r\n    ...mapActions(\"repair_modules/repair\", [\r\n      \"getConfigInfoOne\",\r\n       \"categoryList\",\r\n       \"numberVersionList\",\r\n       \"bindOrUnbind\"\r\n    ]),\r\n     ...mapMutations(\"repair_modules/repair\", [\"setScanCode\"]),\r\n    onClickLeft(){\r\n      this.$router.go(-1)\r\n    },\r\n    selectItem(index,item) {\r\n        console.log(item);\r\n        // this.selectedItem=item\r\n        this.popuptitle=item.trackCircuitConfig.drawingName\r\n      // 如果点击的是已选中的项，则取消选择，否则选中该项\r\n      this.selectedIndex = this.selectedIndex === index ? null : index;\r\n      this.getdata(item.trackCircuitConfig.id)\r\n    },\r\n    async getcategoryList(){\r\n        const res=await this.categoryList({\r\n          stationId:this.$route.query.stationId\r\n        })\r\n        if(res.code==0){\r\n            \r\n          this.trackData = this.$route.query.type==1? res.data[0]:res.data[1]\r\n          this.popuptitle=this.$route.query.type==1?\r\n           res.data[0].children[0].trackCircuitConfig.drawingName:res.data[1].children[0].trackCircuitConfig.drawingName\r\n          if(this.$route.query.type==1){\r\n              this.getdata(this.trackData.children[0].trackCircuitConfig.id)\r\n          }else{\r\n            this.getdata(this.trackData.children[0].trackCircuitConfig.id)\r\n          }\r\n        \r\n        }\r\n    },\r\n    async getdata(categoryId){\r\n        const res=await this.getConfigInfoOne({\r\n            configId:categoryId,\r\n            stationId:this.$route.query.stationId,\r\n            categoryId:this.$route.query.categoryId,\r\n        })\r\n        if(res.code==0){\r\n            this.titleList=res.data.sectionInfoList[0].configInfoList\r\n            this.sectionInfoList=res.data.sectionInfoList\r\n            \r\n        }\r\n    },\r\n     // 判断是否在微信环境\r\n    isMiniProgram(callback) {\r\n      var ua = window.navigator.userAgent.toLowerCase();\r\n      if (ua.match(/MicroMessenger/i) != \"micromessenger\") {\r\n        callback(false);\r\n      } else {\r\n        wx.miniProgram.getEnv((res) => {\r\n          if (res.miniprogram) {\r\n            callback(true); //小程序环境\r\n          } else {\r\n            callback(false);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    async checkTypeFun(item,value){\r\n       \r\n        if(value.isUse){\r\n             this.unhire(value)\r\n        }else{\r\n             localStorage.setItem('sectionInfoItem',JSON.stringify(value))\r\n             this.isMiniProgram((res)=>{\r\n                if(res){\r\n                     this.isWeChat = true;\r\n                            this.$router.push({\r\n            path:'/repair/scanCode',\r\n            query:{\r\n                type:'courierCode'\r\n            }\r\n           })\r\n                }else{\r\n                     this.isWeChat = false;\r\n                     this.$router.push({\r\n                    path:'trackCircuit/qrcode',\r\n                })\r\n                }\r\n             })\r\n       \r\n             //    this.$router.push({\r\n        //     path:'/repair/scanCode',\r\n        //     query:{\r\n        //         type:'courierCode'\r\n        //     }\r\n        //    })\r\n        }\r\n      \r\n\r\n    },\r\n    closePopup(){\r\n        this.isusePopup=false\r\n        this.popuptitle=''\r\n        localStorage.removeItem('sectionInfoItem')\r\n         localStorage.removeItem('csqrcode')\r\n    },\r\n    async unhire(value){\r\n        console.log(value);\r\n        \r\n        this.isusePopup=true\r\n          const res=await this.numberVersionList({\r\n            sectionInfoId:value.id\r\n        })\r\n        if(res.code==0){\r\n            this.unhireitem=res.data\r\n        }else{\r\n            this.$toast.fail(res.meg)\r\n        }\r\n    },\r\n     async newunhire(value){\r\n        console.log(value);\r\n        \r\n        this.isusePopup=true\r\n          const res=await this.numberVersionList({\r\n            sectionInfoId:JSON.parse(value).id\r\n        })\r\n        if(res.code==0){\r\n            this.unhireitem=res.data\r\n            if(this.isWeChat){\r\n                 const scanCode = this.scanCodeInfo?.courierCode || \"\";\r\n                 this.unhireitem.number=scanCode\r\n            }else{\r\n                const code= JSON.parse(localStorage.getItem('csqrcode'))\r\n\r\n                this.unhireitem.number=code.decodedText\r\n            }\r\n           \r\n        }else{\r\n            this.$toast.fail(res.meg)\r\n        }\r\n    },\r\n    async unbind(){\r\n        \r\n        this.$dialog.confirm({\r\n            title: '提示',\r\n            message: `是否要${this.unhireitem.isUse?'解绑':'绑定'}`,\r\n            confirmButtonText: `确认${this.unhireitem.isUse?'解绑':'绑定'}`,\r\n        }).then(async() => {\r\n            let obj={\r\n                id:this.unhireitem.id,\r\n                isUse:this.unhireitem.isUse?false:true\r\n            }\r\n            if(!this.unhireitem.isUse){\r\n                obj.number=this.unhireitem.number\r\n                obj.version = this.unhireitem.version\r\n            }\r\n            const res=await this.bindOrUnbind(obj)\r\n            if(res.code==0){\r\n                this.setScanCode({})\r\n                this.$toast.success(`${this.unhireitem.isUse?'解绑':'绑定'}成功！`)\r\n                this.closePopup()\r\n                 this.getcategoryList()\r\n            }else{\r\n                this.$toast.fail(res.msg||'操作失败')\r\n\r\n            }\r\n       })\r\n    },\r\n    onConfirm(value){\r\n        this.unhireitem.version=value.text\r\n        this.versionPopup=false\r\n    },\r\n    onCancel(){\r\n        this.versionPopup=false\r\n    },\r\n    closeversionPopup(){\r\n        this.versionPopup=false\r\n    }\r\n\r\n  },\r\n  async created() {\r\n    this.isMiniProgram((res) => {\r\n      if (res) {\r\n        console.log(\"isMiniProgram\");\r\n        this.isWeChat = true;\r\n      } else {\r\n        this.isWeChat = false;\r\n      }\r\n    });\r\n  },\r\n  mounted() { \r\n    console.log(localStorage.getItem('csqrcode'));\r\n\r\n    \r\n    if( localStorage.getItem('sectionInfoItem')){\r\n        const item=localStorage.getItem('sectionInfoItem')\r\n        if(this.scanCodeInfo.courierCode||localStorage.getItem('csqrcode')){\r\n             this.newunhire(item)\r\n        }\r\n       \r\n    }\r\n    this.getcategoryList()\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.box{\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: #f0f8ff;\r\n  // padding: 8px 10px 0px;\r\n  box-sizing: border-box;\r\n  overflow: auto;\r\n//   display: flex;\r\n//   flex-direction: column;\r\n}\r\n.header{\r\n    width: 100%;\r\n    padding: 10px 20px;\r\n    background-color: #fff;\r\n    display: flex;\r\n    \r\n}\r\n.header-title {\r\n  padding: 5px 10px;\r\n  margin: 5px;\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  border: 1px solid #000;\r\n\r\n}\r\n\r\n.header-title.active {\r\n  background-color: #1989fa;\r\n  color: #fff;\r\n  border: none;\r\n}\r\n.content{\r\n    width: 100%;\r\n    padding: 10px;\r\n    margin-top:10px ;\r\n    background: #fff;\r\n}\r\n.content-box{\r\n    width: 100%;\r\n    display: flex;\r\n}\r\n.content-left{\r\n    width: 15%;\r\n    div{\r\n        width: 100%;\r\n        height: 60px;\r\n        line-height: 60px;\r\n        font-size: 16px;\r\n        text-align: center;\r\n        color: #015a9d;\r\n        font-family: 黑体;\r\n        font-weight: 700;\r\n    }\r\n}\r\n.content-list{\r\n    flex: 1;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-evenly;\r\n    .item{\r\n        width: 19%;\r\n        .item-title{\r\n            color: #575757;\r\n            height: 60px;\r\n            line-height: 60px;\r\n            font-size: 16px;\r\n            font-family: 黑体;\r\n            font-weight: 700;\r\n        }\r\n        .item-isUse{\r\n            height: 60px;\r\n            line-height: 60px;\r\n            font-size: 16px;\r\n            .success-icon{\r\n                display: flex;\r\n                align-items: center;\r\n                color: #07c160;\r\n                cursor: pointer;\r\n                div{\r\n                    width: 20px;\r\n                    height: 20px;\r\n                    border-radius: 50%;\r\n                    display: flex;\r\n                    justify-content: center;\r\n                    align-items: center;\r\n                    border: 1px dashed #07c160\r\n                }\r\n            }\r\n            .error-icon{\r\n                display: flex;\r\n                align-items: center;\r\n                color: #7c7575;\r\n                  cursor: pointer;\r\n                div{\r\n                    width: 15px;\r\n                    height: 15px;\r\n                    border-radius: 50%;\r\n                    \r\n                    border: 1px dashed #a3a3a3\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n// Popup 弹出层样式\r\n.popup-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  background: white;\r\n}\r\n\r\n.popup-header {\r\n  padding: 20px 20px 0 20px;\r\n  border-bottom: 1px solid #eee;\r\n\r\n  .popup-title {\r\n    font-size: 15px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin: 0;\r\n    padding-bottom: 16px;\r\n    text-align: center;\r\n  }\r\n  \r\n}\r\n.popup-sectionName{\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 20px 15px;\r\n    font-weight: 600;\r\n     color: #015a9d;;\r\n  }\r\n  .popup-form{\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    .popup-form-item{\r\n        width: 100%;\r\n        padding: 10px;\r\n\r\n        .popup-form-item-title{\r\n            width: 100%;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            font-size: 14px;\r\n            .title{\r\n                color: #015a9d;\r\n            }\r\n            .number{\r\n                display: flex;\r\n                flex-wrap: wrap;\r\n                width: 60%;\r\n            }\r\n        }\r\n    }\r\n  }\r\n.label-text{\r\n    font-size: 14px;\r\n     color: #015a9d;;\r\n}\r\n.content-monitor{ \r\n    width: 100%;\r\n    .content-monitor-title{\r\n        width: 100%;\r\n        color: #333;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        padding: 10px;\r\n    }\r\n    .content-monitor-item{\r\n        width: 100%;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        // justify-content: space-evenly;\r\n        .monior-item{\r\n            width: 22%;\r\n            height: 60px;\r\n            display: flex;\r\n            justify-content: space-around;\r\n            align-items: center;\r\n            margin: 10px 0;\r\n            flex-direction: column;\r\n            margin: 0 5px;\r\n            .title{\r\n                width: 100%;\r\n                font-size: 14px;\r\n                text-align: center;\r\n            }\r\n            .isusechange{\r\n                font-size: 20px;\r\n                width: 20px;\r\n                height: 20px;\r\n                border-radius: 50%;\r\n                \r\n                border: 1px dashed #a3a3a3\r\n            }\r\n            .isusepass{\r\n                font-size: 20px;\r\n                width: 20px;\r\n                height: 20px;\r\n                display: flex;\r\n                justify-content: center;\r\n                align-items: center;\r\n                border-radius: 50%;\r\n                color: #fff;\r\n                background: #07c160;\r\n                border: 1px dashed #07c160\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"]}]}